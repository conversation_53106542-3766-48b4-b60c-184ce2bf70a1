#!/usr/bin/env python3
"""
人脸分析器全局配置
"""

import torch


class Config:
    """
    人脸分析器配置类
    统一管理所有配置变量
    """

    # ============================================================================
    # 人脸检测相关配置
    # ============================================================================

    # ============================================================================
    # BasicFilter 过滤器配置
    # ============================================================================
    MIN_CONFIDENCE = 0.97  # 最小置信度阈值
    MAX_CONFIDENCE = 0.99
    MIN_FILTER_FACE_SIZE = 30.0  # BasicFilter 最小人脸尺寸（像素）
    MIN_FILTER_FACE_RATIO = 0.1  # BasicFilter 最小人脸占画面比例

    # ============================================================================
    # HopeNet相关配置
    # ============================================================================
    ENABLE_HOPENET = True  # 默认禁用HopeNet功能，可通过命令行参数启用
    HOPENET_WEIGHTS_PATH = '/home/<USER>/video_summerization/test_script/face_analyzer/models/hopenet/hopenet_robust_alpha1.pkl'
    DEEP_HEAD_POSE_PATH = '/home/<USER>/video_summerization/test_script/face_analyzer/models/hopenet'



    # ============================================================================
    # 调试可视化配置
    # ============================================================================
    DEBUG_FACE_VISUALIZATION = True  # 人脸检测调试可视化开关
    DEBUG_OUTPUT_DIR = "debug_output"  # 调试输出目录
    DEBUG_MVEXTRACTOR_VISUALIZATION = True  # MVExtractor调试可视化开关
    DEBUG_MVEXTRACTOR_OUTPUT_DIR = "debug_mvextractor_output"  # MVExtractor调试输出目录
    DEBUG_MVEXTRACTOR_VIDEO_OUTPUT_DIR = "debug_mvextractor_videos"  # MVExtractor调试视频输出目录


    # ============================================================================
    # 视频处理相关配置
    # ============================================================================
    MIN_BRIGHTNESS = 10      # 最小亮度阈值
    MIN_CONTRAST = 5         # 最小对比度阈值
    MOTION_THRESHOLD = 0.5
    # 视频处理器配置
    VIDEO_PROCESSOR_TYPE = "mvextractor"  # 视频处理器类型，支持 "decord"、"cv2" 和 "mvextractor"
                                    # "decord": 使用 Decord 库，性能更好，支持更多格式
                                    # "cv2": 使用 OpenCV，兼容性更好，无额外依赖
                                    # "mvextractor": 使用 MVExtractor 库，支持运动向量分析

    # ============================================================================
    # 设备配置
    # ============================================================================
    @classmethod
    def _get_device(cls):
        """动态获取设备配置"""
        try:
            return 'cuda' if torch.cuda.is_available() else 'cpu'
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"❌ 设备配置错误: {str(e)}\n调用堆栈:\n{error_traceback}")
            return 'cpu'

    DEVICE = _get_device.__func__(None)  # 调用类方法获取设备

    # ============================================================================
    # 模型路径配置
    # ============================================================================
    RETINAFACE_MODEL_PATH = 'models/retinaface/pytorch_model.pt'



    # ============================================================================
    # 过滤链配置
    # ============================================================================
    FILTER_CHAIN_COMPOSITION = ['BasicFilter', 'GeometryFilter']  # 过滤链组成，按顺序执行

    # ============================================================================
    # 帧过滤器配置
    # ============================================================================
    FRAME_FILTER_COMPOSITION = ['QualityFilter']  # 帧过滤器链组成，按顺序执行

    # ============================================================================
    # 视频采样配置
    # ============================================================================
    SAMPLE_INTERVAL_SEC = 3.0  # 视频采样间隔（秒）

    # ============================================================================
    # 视频处理配置
    # ============================================================================
    SEARCH_ALL = False  # 是否搜索所有帧（True: 完整搜索，False: 早期退出）

    # ============================================================================
    # 缓存配置
    # ============================================================================
    CACHE_ENABLED = True  # 是否启用缓存功能
    CACHE_FILE = "face_analysis_cache.json"  # 缓存文件路径

    # ============================================================================
    # WebP 功能配置
    # ============================================================================
    WEBP_ENABLED = True  # WebP功能总开关
    WEBP_SINGLE_ENABLED = True  # 单帧WebP开关
    WEBP_ANIMATION_ENABLED = True  # 动画WebP开关

    # WebP 生成参数
    WEBP_QUALITY = 80  # WebP质量 (0-100)
    WEBP_LOSSLESS = False  # 是否使用无损压缩
    WEBP_ANIMATION_DURATION_SEC = 3.0  # 动画持续时间(秒)
    WEBP_ANIMATION_LOOP = 0  # 循环次数 (0=无限循环)
    WEBP_DEFAULT_RESOLUTION = "640x480"  # 默认分辨率

    # WebP 输出文件命名
    WEBP_OUTPUT_SINGLE_SUFFIX = "_frame"  # 单帧输出后缀
    WEBP_OUTPUT_ANIMATION_SUFFIX = "_anim"  # 动画输出后缀

    # ============================================================================
    # 人脸过滤器评分配置
    # ============================================================================
    FACEFILTER_THRESHOLD = 1.5  # 人脸过滤的得分阈值

    # ============================================================================
    # 视频分割和并行处理配置
    # ============================================================================
    VIDEO_SPLIT_SEGMENTS = 1  # 视频分割段数，同时也是并行处理的进程数

    # ============================================================================
    # 视频预览生成配置
    # ============================================================================
    VIDEO_PREVIEW_ENABLED = True  # 视频预览功能总开关
    VIDEO_PREVIEW_SEGMENT_DURATION = 1.0  # 每个关键帧的视频片段时长（秒）
    VIDEO_PREVIEW_SEGMENT_PADDING = 0.5  # 关键帧前后的填充时长（秒）
    VIDEO_PREVIEW_KMEANS_CLUSTERS = 30  # K-means聚类数量（关键帧数量）
    VIDEO_PREVIEW_OUTPUT_SUFFIX = "_preview"  # 预览视频输出后缀
    VIDEO_PREVIEW_OUTPUT_FORMAT = "mp4"  # 预览视频输出格式
    VIDEO_PREVIEW_VIDEO_CODEC = "libx264"  # 视频编码器
    VIDEO_PREVIEW_AUDIO_CODEC = "aac"  # 音频编码器
    VIDEO_PREVIEW_CRF = 23  # 视频质量参数 (18-28, 越小质量越高)
    VIDEO_PREVIEW_PRESET = "medium"  # 编码速度预设 (ultrafast, fast, medium, slow, veryslow)

    # ============================================================================
    # HTTP API 服务配置
    # ============================================================================
    HTTP_ENABLED = True
    HTTP_HOST = '0.0.0.0'
    HTTP_PORT = 5000
    HTTP_DEBUG = False

    # API配置
    API_VERSION = "1.0.0"
    API_SERVICE_NAME = "Face Analyzer API"

    # 请求限制
    HTTP_MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    HTTP_REQUEST_TIMEOUT = 3600  # 5分钟

    # 并发控制配置
    CONCURRENCY_LIMIT = 4  # 全局最大并发请求数

    # 日志配置
    LOG_OUTPUT_MODE = "both"  # 可选值: "screen", "file", "both", "none"
    LOG_LEVEL = "INFO"  # 可选值: "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"

    # 结果状态日志文件配置
    RESULT_SUCCESS_LOG = "success_log.txt"
    RESULT_ERROR_LOG = "error_log.txt"
    RESULT_PROCESSED_LOG = "processed_results.log"

    SUPPORTED_VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']