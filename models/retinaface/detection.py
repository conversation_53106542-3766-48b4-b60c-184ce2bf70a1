# The implementation is based on resnet, available at https://github.com/biubug6/Pytorch_Retinaface
import numpy as np
import torch
import os
import json

from .utils import PriorBox, decode, decode_landm, py_cpu_nms

# TensorRT相关导入
import tensorrt as trt
import pycuda.driver as cuda

from loguru import logger

# TensorRT日志级别
TRT_LOGGER = trt.Logger(trt.Logger.WARNING)


class TensorRTInferenceEngine:
    """内置的TensorRT推理引擎（GPU优化版本）"""

    def __init__(self, engine_path: str, device='cuda'):
        """
        初始化推理引擎

        Args:
            engine_path: TensorRT引擎文件路径
            device: 设备类型
        """
        self.engine_path = engine_path
        self.device = device
        self.engine = None
        self.context = None
        self.runtime = None
        self.stream = None

        # 预分配缓冲区配置
        self.max_input_shape = (1, 3, 1000, 1000)  # 最大支持1000x1000分辨率
        self.d_input = None
        self.d_outputs = []
        self.h_outputs = []

        self._load_engine()
        self._preallocate_buffers()

    def _load_engine(self):
        """加载TensorRT引擎"""
        try:
            # 确保CUDA上下文
            import pycuda.autoinit  # noqa: F401 确保CUDA上下文初始化

            # 加载引擎
            with open(self.engine_path, 'rb') as f:
                engine_data = f.read()

            self.runtime = trt.Runtime(TRT_LOGGER)
            self.engine = self.runtime.deserialize_cuda_engine(engine_data)
            self.context = self.engine.create_execution_context()

            # 创建CUDA流
            self.stream = cuda.Stream()

        except Exception as e:
            logger.error(f"TensorRT引擎加载失败: {e}")
            raise

    def _preallocate_buffers(self):
        try:
            # 设置最大输入形状
            self.context.set_input_shape("input", self.max_input_shape)

            # 检查输入张量精度
            input_dtype = self.engine.get_tensor_dtype("input")
            if input_dtype == trt.DataType.HALF:
                input_element_size = 2  # FP16 = 2 bytes
                logger.info("TensorRT引擎使用FP16输入精度")
            else:
                input_element_size = 4  # FP32 = 4 bytes
                logger.warning("TensorRT引擎使用FP32输入精度，建议使用FP16引擎")

            # 预分配输入缓冲区（根据实际精度分配）
            max_input_size = int(np.prod(self.max_input_shape) * input_element_size)
            self.d_input = cuda.mem_alloc(max_input_size)
            self.context.set_tensor_address("input", int(self.d_input))

            # 预分配输出缓冲区
            self.d_outputs = []
            self.h_outputs = []

            for i in range(1, self.engine.num_io_tensors):
                tensor_name = self.engine.get_tensor_name(i)
                output_shape = self.context.get_tensor_shape(tensor_name)

                # 根据输出张量精度分配内存
                tensor_dtype = self.engine.get_tensor_dtype(tensor_name)
                if tensor_dtype == trt.DataType.HALF:
                    output_size = int(np.prod(output_shape) * 2)  # FP16 = 2 bytes
                    h_output = np.empty(output_shape, dtype=np.float16)
                    logger.info(f"输出张量 {tensor_name} 使用FP16精度")
                else:
                    output_size = int(np.prod(output_shape) * 4)  # FP32 = 4 bytes
                    h_output = np.empty(output_shape, dtype=np.float32)
                    logger.warning(f"输出张量 {tensor_name} 使用FP32精度")

                # GPU内存分配
                d_output = cuda.mem_alloc(output_size)
                self.d_outputs.append(d_output)
                self.context.set_tensor_address(tensor_name, int(d_output))
                self.h_outputs.append(h_output)

            logger.info(f"预分配缓冲区完成，最大支持尺寸: {self.max_input_shape}")
            logger.info(f"输入精度: {'FP16' if input_dtype == trt.DataType.HALF else 'FP32'}")

        except Exception as e:
            logger.error(f"预分配缓冲区失败: {e}")
            raise

    def _validate_input_shape(self, input_shape):
        """验证输入尺寸是否在预分配范围内"""
        max_batch, max_channels, max_height, max_width = self.max_input_shape
        batch, channels, height, width = input_shape

        if (batch > max_batch or channels > max_channels or
            height > max_height or width > max_width):
            raise ValueError(
                f"输入尺寸 {input_shape} 超出预分配的最大尺寸 {self.max_input_shape}"
            )

        return True

    def _free_buffers(self):
        """释放预分配的缓冲区"""
        if self.d_input:
            self.d_input.free()
            self.d_input = None

        for d_output in self.d_outputs:
            if d_output:
                d_output.free()

        self.d_outputs = []
        self.h_outputs = []

    def infer_from_gpu_tensor(self, gpu_tensor):
        """
        使用预分配缓冲区进行FP16精度GPU推理

        Args:
            gpu_tensor: GPU上的torch张量，形状为(batch, channels, height, width)

        Returns:
            tuple: (loc, conf, landms) 推理结果的GPU torch.float16张量
        """
        input_shape = gpu_tensor.shape

        # 设置当前输入形状（动态调整输出形状）
        self.context.set_input_shape("input", input_shape)

        # 确保所有绑定形状都已正确设置（TensorRT动态形状要求）
        if not self.context.all_binding_shapes_specified:
            logger.error("并非所有绑定形状都已指定")
            raise RuntimeError("TensorRT上下文绑定形状未完全指定")

        # 确保张量是连续的并且是FP16类型（TensorRT FP16引擎要求）
        if not gpu_tensor.is_contiguous():
            gpu_tensor = gpu_tensor.contiguous()

        # 转换为FP16精度（TensorRT FP16引擎要求）
        if gpu_tensor.dtype != torch.float16:
            gpu_tensor = gpu_tensor.to(torch.float16)

        # 优化的GPU内存拷贝：平衡性能和兼容性
        # 消除不必要的精度转换，减少内存传输开销

        # 确保张量是连续的和正确的精度
        if not gpu_tensor.is_contiguous():
            gpu_tensor = gpu_tensor.contiguous()

        # 智能精度转换（避免不必要的FP16→FP32→FP16转换）
        if gpu_tensor.dtype != torch.float16:
            gpu_tensor = gpu_tensor.to(torch.float16)

        # 优化的内存传输（减少中间步骤，保持兼容性）
        gpu_tensor_cpu = gpu_tensor.detach().cpu().numpy()
        cuda.memcpy_htod_async(self.d_input, gpu_tensor_cpu, self.stream)

        # 验证执行上下文状态
        logger.debug(f"执行推理前验证 - 输入形状: {input_shape}")
        logger.debug(f"绑定形状已指定: {self.context.all_binding_shapes_specified}")

        # 执行推理
        self.context.execute_async_v3(self.stream.handle)

        # 复制输出数据到GPU torch张量（FP16精度输出处理）
        gpu_outputs = []
        for i, d_output in enumerate(self.d_outputs):
            # 获取当前输出的实际形状（动态形状支持）
            tensor_name = self.engine.get_tensor_name(i + 1)

            # 确保输出形状已正确计算
            try:
                actual_output_shape = self.context.get_tensor_shape(tensor_name)
                logger.debug(f"输出张量 {tensor_name} 形状: {actual_output_shape}")
            except Exception as e:
                logger.error(f"获取输出张量 {tensor_name} 形状失败: {e}")
                raise RuntimeError(f"无法获取输出张量形状: {tensor_name}")

            # 验证输出形状的有效性
            if any(dim <= 0 for dim in actual_output_shape):
                logger.error(f"输出张量 {tensor_name} 形状无效: {actual_output_shape}")
                raise RuntimeError(f"输出张量形状无效: {tensor_name}")

            # 验证TensorRT引擎输出精度
            tensor_dtype = self.engine.get_tensor_dtype(tensor_name)

            # 将TensorRT Dims转换为tuple
            shape_tuple = tuple(actual_output_shape)

            if tensor_dtype == trt.DataType.HALF:
                # FP16精度输出 - 直接创建GPU张量
                gpu_output = torch.empty(shape_tuple, dtype=torch.float16, device=self.device)
                logger.debug(f"输出张量 {tensor_name} 使用FP16精度")
            else:
                # 如果引擎不是FP16，创建FP32张量后转换为FP16
                gpu_output = torch.empty(shape_tuple, dtype=torch.float32, device=self.device)
                logger.warning(f"输出张量 {tensor_name} 不是FP16精度，将进行转换")

            # 直接从GPU缓冲区复制到GPU张量（避免CPU传输）
            # 先创建临时CPU数组，然后拷贝到GPU张量
            temp_cpu_array = np.empty(shape_tuple, dtype=np.float32 if tensor_dtype != trt.DataType.HALF else np.float16)
            cuda.memcpy_dtoh_async(temp_cpu_array, d_output, self.stream)

            gpu_outputs.append((temp_cpu_array, tensor_dtype))

        # 同步流确保所有数据传输完成
        self.stream.synchronize()

        # 将所有CPU数组转换为GPU张量
        final_outputs = []
        for temp_cpu_array, tensor_dtype in gpu_outputs:
            # 将CPU数组转换为GPU张量
            gpu_output = torch.from_numpy(temp_cpu_array).to(device=self.device)

            # 确保输出为FP16精度
            if gpu_output.dtype != torch.float16:
                gpu_output = gpu_output.to(torch.float16)

            final_outputs.append(gpu_output)

        # 返回GPU torch张量，保持完全的GPU数据流
        return tuple(final_outputs)

    def __del__(self):
        """析构函数，确保内存释放"""
        try:
            self._free_buffers()
        except:
            pass


class RetinaFaceDetection(torch.nn.Module):

    def __init__(self, model_path, device='cuda'):
        # Initialize torch.nn.Module
        torch.nn.Module.__init__(self)
        # Initialize model attributes
        self.model_dir = model_path
        self.model_path = model_path
        self.device = device

        # 加载配置
        config_path = model_path.replace('pytorch_model.pt', 'configuration.json')
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        self.cfg = config_data['models']

        self.trt_engine = None
        self._init_tensorrt()

        # GPU加速预处理配置
        self.use_fp16 = True
        self.torch_dtype = torch.float16

        # GPU上的预处理参数
        self.mean_tensor = torch.tensor([104, 117, 123], dtype=self.torch_dtype, device=self.device).view(1, 3, 1, 1)

        # GPU内存缓冲区管理
        self.gpu_buffers = {}
        self.max_buffer_size = 2048  # 支持最大2048x2048图像

    def _init_tensorrt(self):
        """初始化TensorRT推理引擎"""
        engine_path = self.model_path.replace('pytorch_model.pt', 'retinaface_fp16.trt')

        if not os.path.exists(engine_path):
            raise FileNotFoundError(f"TensorRT引擎文件不存在: {engine_path}")

        try:
            logger.info(f"加载TensorRT引擎: {engine_path}")
            self.trt_engine = TensorRTInferenceEngine(engine_path)
            logger.info("TensorRT引擎初始化成功")
        except Exception as e:
            logger.error(f"TensorRT引擎初始化失败: {e}")
            raise RuntimeError(f"TensorRT引擎初始化失败: {e}")

    def forward(self, input):
        """GPU加速的前向推理"""

        img_input = input['img']
        # 将numpy数组转换为GPU张量：[H, W, C] -> [1, C, H, W]
        img_tensor = torch.from_numpy(img_input).to(device=self.device, dtype=self.torch_dtype)
        img_tensor = img_tensor.permute(2, 0, 1).unsqueeze(0)  # [H,W,C] -> [1,C,H,W]

        # 获取原始尺寸
        _, _, im_height, im_width = img_tensor.shape

        max_dim = max(im_height, im_width)
        ss = torch.tensor(1000.0, dtype=self.torch_dtype, device=self.device) / torch.tensor(max_dim, dtype=self.torch_dtype, device=self.device)

        # 计算新的尺寸
        new_height = int(im_height * ss.item())
        new_width = int(im_width * ss.item())

        # 使用torch.nn.functional.interpolate进行GPU加速缩放
        # 使用area模式以更好地匹配OpenCV的默认行为
        img_tensor = torch.nn.functional.interpolate(
            img_tensor,
            size=(new_height, new_width),
            mode='area'
        )

        # 更新尺寸
        _, _, im_height, im_width = img_tensor.shape

        # GPU加速的图像预处理
        # 减去均值（广播操作）
        img_processed = img_tensor - self.mean_tensor

        # 直接从GPU张量进行TensorRT推理，返回GPU torch张量
        loc, conf, landms = self.trt_engine.infer_from_gpu_tensor(img_processed)

        # 后处理
        scale_factor = ss.item() if ss is not None else 1.0
        return self._postprocess(loc, conf, landms, im_height, im_width, scale_factor)

    def _postprocess(self, loc, conf, landms, im_height, im_width, ss):
        """优化的后处理（GPU张量计算，CPU numpy输出）"""
        # 使用原始精度的阈值
        confidence_threshold = 0.9
        nms_threshold = 0.4
        top_k = 5000
        keep_top_k = 750

        # 创建尺度张量（GPU上的FP16张量）
        scale = torch.tensor([im_width, im_height, im_width, im_height],
                           dtype=torch.float16, device=self.device)
        scale1 = torch.tensor([
            im_width, im_height, im_width, im_height, im_width, im_height,
            im_width, im_height, im_width, im_height
        ], dtype=torch.float16, device=self.device)

        # 生成先验框（GPU张量）
        priorbox = PriorBox(self.cfg, image_size=(im_height, im_width))
        priors = priorbox.forward().to(device=self.device, dtype=torch.float16)

        # TensorRT输出处理（完全GPU张量数据流）
        # 解码边界框（直接使用GPU张量，无需转换）
        boxes = decode(loc.squeeze(0), priors, self.cfg['variance'])
        boxes = boxes * scale

        # 解码置信度（直接使用GPU张量）
        scores = conf.squeeze(0)[:, 1]

        # 解码关键点（直接使用GPU张量，无需转换）
        landms_decoded = decode_landm(landms.squeeze(0), priors, self.cfg['variance'])
        landms_decoded = landms_decoded * scale1

        # NMS处理（GPU张量处理）
        # 过滤低置信度
        valid_inds = scores > confidence_threshold
        if not torch.any(valid_inds):
            # 返回空的CPU numpy数组
            empty_dets = np.empty((0, 5), dtype=np.float16)
            empty_landms = np.empty((0, 10), dtype=np.float16)
            return empty_dets, empty_landms

        boxes = boxes[valid_inds]
        landms_decoded = landms_decoded[valid_inds]
        scores = scores[valid_inds]

        # Top-K选择（GPU张量处理）
        if len(scores) > top_k:
            _, top_inds = torch.topk(scores, top_k)
            boxes = boxes[top_inds]
            landms_decoded = landms_decoded[top_inds]
            scores = scores[top_inds]

        # NMS处理（转换到CPU进行NMS，后续保持CPU numpy处理）
        dets = torch.cat([boxes, scores.unsqueeze(1)], dim=1)

        # 转换到CPU进行NMS（FP16转FP32以确保NMS精度）
        dets_cpu = dets.cpu().float().numpy()
        landms_cpu = landms_decoded.cpu().float().numpy()
        keep = py_cpu_nms(dets_cpu, float(nms_threshold))

        # 保持CPU numpy处理
        dets_cpu = dets_cpu[keep]
        landms_cpu = landms_cpu[keep]

        # 最终Top-K（CPU numpy处理）
        if len(dets_cpu) > keep_top_k:
            dets_cpu = dets_cpu[:keep_top_k]
            landms_cpu = landms_cpu[:keep_top_k]

        # 重塑关键点（CPU numpy处理）
        landms_cpu = landms_cpu.reshape((-1, 5, 2)).reshape(-1, 10)

        # 最终输出（CPU numpy处理，转换为FP16精度）
        result_dets = (dets_cpu / ss).astype(np.float16)
        result_landms = (landms_cpu / ss).astype(np.float16)

        return result_dets, result_landms

    def __call__(self, *args, **kwargs):
        """Call the model"""
        return self.forward(*args, **kwargs)

