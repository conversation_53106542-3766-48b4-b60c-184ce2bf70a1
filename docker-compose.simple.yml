version: '3.8'

services:
  # 主应用服务
  face-analyzer:
    build:
      context: ..
      dockerfile: face_analyzer/Dockerfile
    image: face-analyzer:latest
    container_name: face-analyzer-app
    restart: unless-stopped
    
    # GPU支持配置
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射
    ports:
      - "5000:5000"
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility,video
      - CUDA_VISIBLE_DEVICES=all
      - PYTHONPATH=/app
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - LOG_LEVEL=INFO
      - MAX_CONCURRENT_REQUESTS=5
      - ENABLE_GPU=true
      - ENABLE_REDIS=false
    
    # 卷挂载
    volumes:
      # 模型文件持久化
      - ./models:/app/models
      # 日志目录
      - ./logs:/app/logs
      # 临时文件目录
      - ./temp:/app/temp
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 资源限制
    mem_limit: 8g
    mem_reservation: 4g
    cpus: '4.0'
    
    # 安全配置
    security_opt:
      - no-new-privileges:true

# 如果需要外部访问，可以取消注释以下网络配置
# networks:
#   default:
#     driver: bridge
