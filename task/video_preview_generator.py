#!/usr/bin/env python3
"""
视频预览生成模块
基于K-means算法挑选关键帧，生成视频预览
"""

import os
import cv2
import numpy as np
import tempfile
import subprocess

from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass

# 创建日志记录器
from loguru import logger

from config.settings import Config
from classes.process_result import ProcessResult


@dataclass
class VideoSegment:
    """视频片段数据类"""
    start_time: float  # 开始时间（秒）
    end_time: float    # 结束时间（秒）
    key_frame_idx: int # 关键帧索引
    key_frame_time: float  # 关键帧时间（秒）


class KeyFrameSelector:
    """基于K-means的关键帧选择器"""
    
    def __init__(self, n_clusters: int = Config.VIDEO_PREVIEW_KMEANS_CLUSTERS):
        """
        初始化关键帧选择器
        
        Args:
            n_clusters: K-means聚类数量
        """
        self.n_clusters = n_clusters
    
    def extract_frame_features_with_scores(self, video_path: str, valid_frames: Dict[float, List[int]]) -> Tuple[np.ndarray, List[Tuple[float, float, int]]]:
        """
        基于k-means_example.py的uniform_face_selection设计提取帧特征
        使用(归一化时间, 质量得分)作为二维特征空间

        Args:
            video_path: 视频文件路径
            valid_frames: FaceProcessor的处理结果，按得分分组的帧索引

        Returns:
            特征矩阵 (n_frames, 2) 和帧信息列表 [(normalized_time, score, frame_idx)]
        """
        # 获取视频总帧数
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频文件: {video_path}")

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        cap.release()

        # 构造特征矩阵：[(t_i, q_i)]
        # t_i 为归一化时间戳 = frame_idx / total_frames
        # q_i 为质量得分
        feats: List[Tuple[float, float, int]] = []
        for score, frames in valid_frames.items():
            for frame_idx in frames:
                normalized_time = frame_idx / total_frames if total_frames > 0 else 0
                feats.append((normalized_time, float(score), frame_idx))

        # 构建特征矩阵 X = [[t, q], ...]
        X = np.array([[t, q] for t, q, _ in feats])  # shape = (M, 2)

        return X, feats
    
    def select_key_frames(self, video_path: str, valid_frames: Dict[float, List[int]]) -> List[int]:
        """
        基于k-means_example.py的uniform_face_selection设计选择关键帧
        使用(归一化时间, 质量得分)作为二维特征空间进行K-means聚类

        Args:
            video_path: 视频文件路径
            valid_frames: FaceProcessor的处理结果，按得分分组的帧索引

        Returns:
            选中的关键帧索引列表，按时间顺序排序
        """
        if not valid_frames:
            return []

        # 计算总的有效帧数
        total_valid_frames = sum(len(indices) for indices in valid_frames.values())

        # 如果有效帧数少于聚类数，直接返回所有帧（按时间排序）
        if total_valid_frames <= self.n_clusters:
            all_frames = []
            for score, indices in valid_frames.items():
                all_frames.extend(indices)
            return sorted(list(set(all_frames)))

        # 使用改进的特征提取方法
        X, feats = self.extract_frame_features_with_scores(video_path, valid_frames)

        if len(X) == 0:
            return []

        # 执行K-means聚类（与k-means_example.py保持一致）
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=self.n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(X)
        cluster_centers = kmeans.cluster_centers_

        # 对每一簇，取最靠近中心的帧（"簇内中位点"）
        selected_frames = []
        for cluster_id in range(self.n_clusters):
            idx_in_cluster = np.where(cluster_labels == cluster_id)[0]
            if len(idx_in_cluster) == 0:
                continue

            # 计算每个点到中心的 L2 距离
            distances = np.linalg.norm(X[idx_in_cluster] - cluster_centers[cluster_id], axis=1)
            best_idx = idx_in_cluster[np.argmin(distances)]

            # 将原始 frame_idx 加入结果
            selected_frames.append(feats[best_idx][2])  # feats[i] = (normalized_time, score, frame_idx)

        # 按时间顺序排序
        return sorted(selected_frames)


class VideoPreviewGenerator:
    """视频预览生成器"""
    
    def __init__(self):
        """初始化视频预览生成器"""
        self.key_frame_selector = KeyFrameSelector()
    
    def get_video_info(self, video_path: str) -> Tuple[float, float]:
        """
        获取视频基本信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            (fps, duration) 元组
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频文件: {video_path}")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        return fps, duration
    
    def frame_idx_to_time(self, frame_idx: int, fps: float) -> float:
        """
        将帧索引转换为时间
        
        Args:
            frame_idx: 帧索引
            fps: 视频帧率
            
        Returns:
            时间（秒）
        """
        return frame_idx / fps if fps > 0 else 0
    
    def calculate_segments(self, key_frame_indices: List[int], fps: float, duration: float) -> List[VideoSegment]:
        """
        计算视频片段，处理重叠和边界情况
        
        Args:
            key_frame_indices: 关键帧索引列表
            fps: 视频帧率
            duration: 视频总时长
            
        Returns:
            视频片段列表
        """
        if not key_frame_indices:
            return []
        
        segments = []
        
        for frame_idx in key_frame_indices:
            key_frame_time = self.frame_idx_to_time(frame_idx, fps)
            
            # 计算片段的开始和结束时间
            start_time = max(0, key_frame_time - Config.VIDEO_PREVIEW_SEGMENT_PADDING)
            end_time = min(duration, key_frame_time + Config.VIDEO_PREVIEW_SEGMENT_PADDING)

            # 确保片段时长不超过配置的最大时长
            segment_duration = end_time - start_time
            if segment_duration > Config.VIDEO_PREVIEW_SEGMENT_DURATION:
                # 如果片段过长，以关键帧为中心裁剪
                half_duration = Config.VIDEO_PREVIEW_SEGMENT_DURATION / 2
                start_time = max(0, key_frame_time - half_duration)
                end_time = min(duration, key_frame_time + half_duration)
            
            segments.append(VideoSegment(
                start_time=start_time,
                end_time=end_time,
                key_frame_idx=frame_idx,
                key_frame_time=key_frame_time
            ))
        
        # 处理重叠片段
        merged_segments = self._merge_overlapping_segments(segments)
        
        return merged_segments
    
    def _merge_overlapping_segments(self, segments: List[VideoSegment]) -> List[VideoSegment]:
        """
        合并重叠的视频片段
        
        Args:
            segments: 原始片段列表
            
        Returns:
            合并后的片段列表
        """
        if not segments:
            return []
        
        # 按开始时间排序
        segments.sort(key=lambda x: x.start_time)
        
        merged = [segments[0]]
        
        for current in segments[1:]:
            last_merged = merged[-1]
            
            # 检查是否重叠
            if current.start_time <= last_merged.end_time:
                # 合并片段
                merged[-1] = VideoSegment(
                    start_time=last_merged.start_time,
                    end_time=max(last_merged.end_time, current.end_time),
                    key_frame_idx=last_merged.key_frame_idx,  # 保留第一个关键帧
                    key_frame_time=last_merged.key_frame_time
                )
            else:
                # 不重叠，添加新片段
                merged.append(current)
        
        return merged

    def extract_video_segments(self, video_path: str, segments: List[VideoSegment], temp_dir: str) -> List[str]:
        """
        使用FFmpeg提取视频片段

        Args:
            video_path: 原视频路径
            segments: 视频片段列表
            temp_dir: 临时目录

        Returns:
            提取的片段文件路径列表
        """
        segment_paths = []

        for i, segment in enumerate(segments):
            segment_path = os.path.join(temp_dir, f"segment_{i:03d}.{Config.VIDEO_PREVIEW_OUTPUT_FORMAT}")

            # 构建FFmpeg命令
            cmd = [
                'ffmpeg', '-y',  # -y 覆盖输出文件
                '-i', video_path,
                '-ss', str(segment.start_time),  # 开始时间
                '-t', str(segment.end_time - segment.start_time),  # 持续时间
                '-c:v', Config.VIDEO_PREVIEW_VIDEO_CODEC,  # 视频编码器
                '-c:a', Config.VIDEO_PREVIEW_AUDIO_CODEC,  # 音频编码器
                '-crf', str(Config.VIDEO_PREVIEW_CRF),  # 质量参数
                '-preset', Config.VIDEO_PREVIEW_PRESET,  # 编码预设
                segment_path
            ]

            try:
                # 执行FFmpeg命令
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                segment_paths.append(segment_path)
            except subprocess.CalledProcessError as e:
                print(f"警告: 提取片段 {i} 失败: {e}")
                print(f"FFmpeg错误输出: {e.stderr}")
                continue

        return segment_paths

    def concatenate_segments(self, segment_paths: List[str], output_path: str, temp_dir: str) -> bool:
        """
        使用FFmpeg拼接视频片段

        Args:
            segment_paths: 片段文件路径列表
            output_path: 输出文件路径
            temp_dir: 临时目录

        Returns:
            拼接是否成功
        """
        if not segment_paths:
            return False

        # 创建FFmpeg concat文件
        concat_file = os.path.join(temp_dir, "concat_list.txt")
        with open(concat_file, 'w', encoding='utf-8') as f:
            for segment_path in segment_paths:
                f.write(f"file '{segment_path}'\n")

        # 构建FFmpeg拼接命令
        cmd = [
            'ffmpeg', '-y',
            '-f', 'concat',
            '-safe', '0',
            '-i', concat_file,
            '-c', 'copy',  # 直接复制，不重新编码
            output_path
        ]

        try:
            # 执行FFmpeg命令
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"拼接视频失败: {e}")
            logger.error(f"FFmpeg错误输出: {e.stderr}")
            return False

    def generate_preview(self, video_path: str, process_result: ProcessResult, output_path: str) -> bool:
        """
        生成视频预览

        Args:
            video_path: 原视频路径
            process_result: FaceProcessor的处理结果
            output_path: 输出预览视频路径

        Returns:
            生成是否成功
        """
        if not Config.VIDEO_PREVIEW_ENABLED:
            return False

        if not process_result.valid_frames:
            logger.warning("没有有效帧，无法生成预览")
            return False

        try:
            # 获取视频信息
            fps, duration = self.get_video_info(video_path)

            # 选择关键帧
            key_frame_indices = self.key_frame_selector.select_key_frames(video_path, process_result.valid_frames)

            if not key_frame_indices:
                logger.warning("没有选中关键帧，无法生成预览")
                return False

            logger.info(f"选中 {len(key_frame_indices)} 个关键帧: {key_frame_indices}")

            # 计算视频片段
            segments = self.calculate_segments(key_frame_indices, fps, duration)

            if not segments:
                logger.warning("没有有效片段，无法生成预览")
                return False

            logger.info(f"计算出 {len(segments)} 个视频片段")

            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                # 提取视频片段
                segment_paths = self.extract_video_segments(video_path, segments, temp_dir)

                if not segment_paths:
                    logger.error("没有成功提取任何视频片段")
                    return False

                logger.info(f"成功提取 {len(segment_paths)} 个视频片段")

                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # 拼接片段
                success = self.concatenate_segments(segment_paths, output_path, temp_dir)

                if success:
                    logger.info(f"预览视频生成成功: {output_path}")
                    return True
                else:
                    logger.error("预览视频拼接失败")
                    return False

        except Exception as e:
            logger.error(f"生成预览视频时发生异常: {e}")
            import traceback
            traceback.print_exc()
            return False


def generate_video_preview(video_path: str, process_result: ProcessResult, output_path: str) -> bool:
    """
    生成视频预览的便捷函数

    Args:
        video_path: 原视频路径
        process_result: FaceProcessor的处理结果
        output_path: 完整的输出文件路径

    Returns:
        生成是否成功
    """
    if not Config.VIDEO_PREVIEW_ENABLED:
        return False

    # 创建生成器并生成预览
    generator = VideoPreviewGenerator()
    return generator.generate_preview(video_path, process_result, output_path)
