#!/usr/bin/env python3
"""
人脸分析器缓存管理器 - TinyDB重构版本
"""

import os
import time
import hashlib
import cv2
import threading
import atexit
from typing import Optional, Dict, Any, List, Union
from collections import defaultdict

from tinydb import TinyDB, Query
from tinydb.storages import MemoryStorage
from loguru import logger

from config.settings import Config
from classes.process_result import ProcessResult


class FaceAnalyzerCacheManager:
    """人脸分析器缓存管理器 - TinyDB重构版本"""

    def __init__(self, flush_interval: float = 8.0):
        """
        初始化缓存管理器

        Args:
            flush_interval (float): 异步刷盘间隔（秒），默认8秒
        """

        # TinyDB数据库实例 - 直接使用现有JSON文件
        self.db = TinyDB(Config.CACHE_FILE)
        self.cache_table = self.db.table('cache')

        # 内存缓存：用于高速读写，格式与JSON兼容
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.dirty_keys: set = set()  # 标记需要持久化的键

        # 异步刷盘配置
        self.flush_interval = flush_interval
        self.flush_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()

        # 启动异步刷盘线程
        self._start_flush_thread()

        # 注册退出时的清理函数
        atexit.register(self._cleanup)

        # 初始化时加载现有数据到内存
        self._load_existing_data()

        logger.info(f"TinyDB缓存管理器初始化完成，JSON文件: {Config.CACHE_FILE}")

    def _start_flush_thread(self):
        """启动异步刷盘线程"""
        if self.flush_thread is None or not self.flush_thread.is_alive():
            self.flush_thread = threading.Thread(
                target=self._flush_worker,
                name="CacheFlushThread",
                daemon=True
            )
            self.flush_thread.start()
            logger.debug("异步刷盘线程已启动")

    def _flush_worker(self):
        """异步刷盘工作线程"""
        while not self.stop_event.is_set():
            try:
                # 等待刷盘间隔或停止信号
                if self.stop_event.wait(timeout=self.flush_interval):
                    break

                # 执行刷盘操作
                self._flush_to_disk()

            except Exception as e:
                logger.error(f"异步刷盘线程异常: {e}")

    def _flush_to_disk(self):
        """将内存中的脏数据刷写到磁盘，直接使用JSON文件操作"""
        if not self.dirty_keys:
            return

        # 复制并清空脏数据集合（单线程操作，无需锁保护）
        dirty_keys_copy = self.dirty_keys.copy()
        self.dirty_keys.clear()

        if not dirty_keys_copy:
            return

        try:
            import json
            import shutil

            # 构建完整的缓存数据结构，保持与原JSON格式兼容
            cache_data = {"cache": {}}

            # 从内存缓存构建完整的JSON结构
            for signature, cache_entry in self.memory_cache.items():
                # 移除signature字段，保持与原JSON格式一致
                clean_entry = {k: v for k, v in cache_entry.items() if k != 'signature'}
                cache_data["cache"][signature] = clean_entry

            # 使用临时文件安全写入
            temp_file = Config.CACHE_FILE + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

            # 原子性替换
            shutil.move(temp_file, Config.CACHE_FILE)

            updated_count = len(dirty_keys_copy)
            if updated_count > 0:
                logger.debug(f"异步刷盘完成，更新了 {updated_count} 条记录")

        except Exception as e:
            logger.error(f"刷盘操作失败: {e}")
            # 将失败的键重新标记为脏数据（单线程操作，无需锁保护）
            self.dirty_keys.update(dirty_keys_copy)

    def _load_existing_data(self):
        """加载现有JSON格式数据到内存缓存"""
        try:
            # 直接从JSON文件加载，避免TinyDB的数据类型转换问题
            if os.path.exists(Config.CACHE_FILE):
                import json
                with open(Config.CACHE_FILE, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)

                loaded_count = 0
                if "cache" in json_data:
                    cache_entries = json_data["cache"]
                    for signature, cache_data in cache_entries.items():
                        # 为内存缓存添加signature字段
                        memory_entry = cache_data.copy()
                        memory_entry["signature"] = signature
                        self.memory_cache[signature] = memory_entry
                        loaded_count += 1

                logger.info(f"加载了 {loaded_count} 条缓存记录到内存")
            else:
                logger.info("缓存文件不存在，从空缓存开始")

        except Exception as e:
            logger.error(f"加载现有数据失败: {e}")

    def _cleanup(self):
        """清理资源，确保数据持久化"""
        logger.info("开始清理缓存管理器资源...")

        # 停止刷盘线程
        self.stop_event.set()

        # 最后一次刷盘
        self._flush_to_disk()

        # 等待刷盘线程结束
        if self.flush_thread and self.flush_thread.is_alive():
            self.flush_thread.join(timeout=5.0)

        # 关闭数据库
        if hasattr(self, 'db'):
            self.db.close()

        logger.info("缓存管理器资源清理完成")

    def get_file_signature(self, file_path: str) -> str:
        """基于文件路径、大小、修改时间生成MD5签名"""
        abs_path = os.path.abspath(file_path)
        stat = os.stat(abs_path)
        signature_data = f"{abs_path}:{stat.st_size}:{stat.st_mtime}"
        return hashlib.md5(signature_data.encode()).hexdigest()

    def get_cached_result(self, file_path: str) -> Optional[Dict]:
        """获取缓存的结果（优先从内存缓存读取）"""
        file_signature = self.get_file_signature(file_path)

        # 优先从内存缓存读取
        cached_info = self.memory_cache.get(file_signature)

        if cached_info:
            # 验证文件是否仍然存在且未被修改
            if os.path.exists(file_path):
                current_signature = self.get_file_signature(file_path)
                if current_signature == file_signature:
                    logger.debug(f"缓存命中（内存）: {file_path}")
                    return {
                        "file_path": cached_info["file_path"],
                        "valid_frames": cached_info["valid_frames"],
                        "cached_at": cached_info["cached_at"]
                    }
                else:
                    # 文件已修改，移除过期缓存
                    self._remove_cache_entry(file_signature)

        logger.debug(f"缓存未命中: {file_path}")
        return None

    def cache_result(self, file_path: str, score_groups: Dict[float, List[int]]) -> None:
        """缓存结果到内存，保持JSON格式兼容"""
        file_signature = self.get_file_signature(file_path)

        # 创建新的缓存条目，保持与原JSON格式一致
        valid_frames_str = {str(score): sorted(indices) for score, indices in score_groups.items()}
        cache_entry = {
            "signature": file_signature,  # 内存中保留signature用于索引
            "file_path": os.path.abspath(file_path),
            "valid_frames": valid_frames_str,
            "cached_at": time.time()
        }

        # 存储到内存缓存
        self.memory_cache[file_signature] = cache_entry

        # 标记为脏数据，需要异步持久化（单线程刷盘，无需锁保护）
        self.dirty_keys.add(file_signature)

        logger.debug(f"缓存已更新（内存）: {file_path}")

    def _remove_cache_entry(self, signature: str):
        """移除缓存条目，保持JSON格式兼容"""
        # 从内存缓存移除
        if signature in self.memory_cache:
            del self.memory_cache[signature]

        # 标记为脏数据，让异步刷盘时更新整个JSON结构（单线程刷盘，无需锁保护）
        self.dirty_keys.add(signature)  # 添加到脏数据，刷盘时会处理删除

        logger.debug(f"缓存条目已标记删除: {signature}")
    
    def clear_cache(self) -> bool:
        """清空所有缓存，直接使用JSON文件操作"""
        try:
            import json

            # 清空内存缓存
            self.memory_cache.clear()

            # 清空脏数据标记（单线程刷盘，无需锁保护）
            self.dirty_keys.clear()

            # 直接写入空的JSON结构
            empty_cache = {"cache": {}}
            with open(Config.CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(empty_cache, f, ensure_ascii=False, indent=2)

            logger.info("缓存已清空")
            return True

        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            json_file = Config.CACHE_FILE
            json_size = os.path.getsize(json_file) if os.path.exists(json_file) else 0

            # 直接从JSON文件计算条目数
            json_cache_entries = 0
            if os.path.exists(json_file):
                import json
                with open(json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    if "cache" in json_data:
                        json_cache_entries = len(json_data["cache"])

            memory_entries = len(self.memory_cache)
            dirty_entries = len(self.dirty_keys)

            return {
                "json_file": json_file,
                "json_cache_entries": json_cache_entries,
                "memory_entries": memory_entries,
                "dirty_entries": dirty_entries,
                "json_exists": os.path.exists(json_file),
                "json_size_bytes": json_size,
                "flush_interval": self.flush_interval,
                "flush_thread_alive": self.flush_thread.is_alive() if self.flush_thread else False
            }

        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {e}")
            return {
                "error": str(e),
                "json_file": Config.CACHE_FILE,
                "json_cache_entries": 0,
                "memory_entries": len(self.memory_cache),
                "dirty_entries": len(self.dirty_keys),
                "json_exists": os.path.exists(Config.CACHE_FILE),
                "json_size_bytes": 0,
                "flush_interval": self.flush_interval,
                "flush_thread_alive": self.flush_thread.is_alive() if self.flush_thread else False
            }

    def force_flush(self) -> bool:
        """强制立即刷盘"""
        try:
            self._flush_to_disk()
            logger.info("强制刷盘完成")
            return True
        except Exception as e:
            logger.error(f"强制刷盘失败: {e}")
            return False


def process_with_cache(input_path: str, cached_data: dict) -> ProcessResult:
    """使用缓存数据处理视频"""

    valid_frames_str = cached_data['valid_frames']
    # 将字符串键转换回浮点数
    valid_frames = {float(score): indices for score, indices in valid_frames_str.items()}

    return ProcessResult(
        success=True,
        video_path=input_path,
        valid_frames=valid_frames
    )





def extract_and_save_cached_frame(video_path: str, output_path: str, frame_idx: Union[int, List[int]], face_processor) -> bool:
    """提取并保存缓存指定的帧（支持单个或多个帧索引）"""
    try:
        # 确保frame_idx是列表格式
        if isinstance(frame_idx, int):
            frame_idx_list = [frame_idx]
        else:
            frame_idx_list = frame_idx

        # 直接使用OpenCV跳转到指定帧索引
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return False

        success_count = 0
        for idx in frame_idx_list:
            # 跳转到指定帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)

            # 读取帧
            ret, frame = cap.read()

            if ret:
                # 保存帧（只保存第一个成功的帧作为主要结果）
                if success_count == 0:
                    face_processor.result_handler.save_result(
                        frame, output_path, 0, is_final=True, frame_idx=idx
                    )
                success_count += 1

        cap.release()
        logger.debug(f"成功提取并保存了 {success_count} 帧")
        return success_count > 0

    except Exception as e:
        logger.error(f"提取并保存缓存帧失败: {e}")
        return False
