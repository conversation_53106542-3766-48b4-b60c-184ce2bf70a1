#!/usr/bin/env python3
"""
WebP动画生成模块
"""

import os
import cv2
import numpy as np
from typing import List, Optional
from PIL import Image

from config.settings import Config
from utils.image_utils import parse_resolution, resize_frame_with_aspect_ratio
from module.video_processors import DecordVideoProcessor

from loguru import logger


def get_video_fps(video_path: str) -> float:
    """
    获取视频的真实帧率

    Args:
        video_path: 视频文件路径

    Returns:
        float: 视频帧率，如果获取失败返回0
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return 0.0

        fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()

        # 验证FPS值的合理性
        if fps <= 0 or fps > 120:
            return 0.0

        return fps

    except Exception as e:
        logger.error(f"视频编解码错误: 无法获取视频FPS {video_path}: {e}")
        return 0.0


def generate_webp_animation(video_path: str, result, output_path: str,
                           duration_frames: Optional[int] = None,
                           resolution: Optional[str] = None) -> bool:
    """
    从视频处理结果生成WebP动画

    Args:
        video_path: 视频文件路径
        result: FaceProcessor的处理结果
        output_path: 输出WebP动画文件路径
        duration_frames: 动画帧数，如果为None则基于视频真实FPS计算
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        bool: 生成是否成功
    """
    # 从处理结果中计算start_frame_idx
    if not result.valid_frames:
        logger.warning(f"{video_path}没有有效人脸帧，从第0帧开始生成动画")
        start_frame_idx = 0
        # return False
    else:
        # 获取最高得分的第一个帧索引
        highest_score = max(result.valid_frames.keys())
        start_frame_idx = result.valid_frames[highest_score][0]

    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    # 动态获取视频FPS
    video_fps = get_video_fps(video_path)
    if video_fps <= 0:
        return False

    # 计算动画帧数（基于视频真实FPS）
    if duration_frames is None:
        duration_frames = int(Config.WEBP_ANIMATION_DURATION_SEC * video_fps)

    # 收集动画帧
    frames = collect_animation_frames(video_path, start_frame_idx, duration_frames, resolution)

    if not frames:
        return False

    # 转换为PIL Images
    pil_images = []
    for frame in frames:
        # 转换为RGB格式（PIL需要）
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        pil_images.append(pil_image)

    # 计算每帧持续时间（毫秒），使用视频真实FPS
    frame_duration = int(1000 / video_fps)

    # 保存为WebP动画
    pil_images[0].save(
        output_path,
        'WEBP',
        save_all=True,
        append_images=pil_images[1:],
        duration=frame_duration,
        loop=Config.WEBP_ANIMATION_LOOP,
        quality=Config.WEBP_QUALITY,
        lossless=Config.WEBP_LOSSLESS
    )

    return True


def collect_animation_frames(video_path: str, start_frame_idx: int,
                           duration_frames: int, resolution: Optional[str] = None) -> List[np.ndarray]:
    """
    收集动画帧（连续帧收集，保持原始视频播放速度）

    Args:
        video_path: 视频文件路径
        start_frame_idx: 开始帧索引
        duration_frames: 需要收集的帧数（基于视频真实FPS计算）
        resolution: 可选的分辨率参数

    Returns:
        List[np.ndarray]: 收集到的帧列表
    """
    frames = []

    # 创建DecordVideoProcessor实例
    try:
        processor = DecordVideoProcessor()
    except Exception as e:
        logger.error(f"无法创建DecordVideoProcessor: {e}")
        return frames

    # 使用DecordVideoProcessor获取视频总帧数（用于边界检查）
    try:
        video_info = processor.get_video_info(video_path)
        total_frames = video_info['total_frames']
    except Exception as e:
        logger.error(f"无法获取视频信息: {video_path}, 错误: {e}")
        return frames

    # 计算实际可收集的帧数
    max_available_frames = total_frames - start_frame_idx
    actual_frames = min(duration_frames, max_available_frames)

    if actual_frames <= 0:
        logger.warning(f"无有效帧可收集: start_frame_idx={start_frame_idx}, total_frames={total_frames}")
        return frames

    # 使用get_frame_by_index方法连续收集帧
    for i in range(actual_frames):
        current_frame_idx = start_frame_idx + i

        try:
            # 使用DecordVideoProcessor的get_frame_by_index方法读取帧
            frame = processor.get_frame_by_index(video_path, current_frame_idx)

            # 调整分辨率
            if resolution:
                target_width, target_height = parse_resolution(resolution)
                frame = resize_frame_with_aspect_ratio(frame, target_width, target_height)
            elif Config.WEBP_DEFAULT_RESOLUTION:
                target_width, target_height = parse_resolution(Config.WEBP_DEFAULT_RESOLUTION)
                frame = resize_frame_with_aspect_ratio(frame, target_width, target_height)

            frames.append(frame)

        except (IndexError, IOError, ValueError) as e:
            logger.warning(f"读取帧 {current_frame_idx} 失败: {e}")
            # 遇到错误时停止收集，返回已收集的帧
            break
        except Exception as e:
            logger.error(f"读取帧 {current_frame_idx} 时发生未知错误: {e}")
            break

    logger.info(f"成功收集 {len(frames)} 帧，从帧 {start_frame_idx} 开始")
    return frames


def calculate_animation_frames(video_fps: float) -> int:
    """
    根据视频FPS计算动画帧数（基于配置的持续时间）

    Args:
        video_fps: 视频帧率

    Returns:
        int: 动画帧数
    """
    # 直接基于视频FPS和配置的持续时间计算帧数
    # 不再进行跳帧，保持原始播放速度
    animation_frames = int(Config.WEBP_ANIMATION_DURATION_SEC * video_fps)

    return max(1, animation_frames)



