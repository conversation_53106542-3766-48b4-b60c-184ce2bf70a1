version: '3.8'

services:
  face-analyzer:
    build:
      context: ..
      dockerfile: face_analyzer/Dockerfile
    image: face-analyzer:latest
    container_name: face-analyzer-app
    restart: unless-stopped
    
    # GPU支持配置
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 端口映射
    ports:
      - "5000:5000"
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility,video
      - CUDA_VISIBLE_DEVICES=all
      - PYTHONPATH=/app
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - LOG_LEVEL=INFO
      - MAX_CONCURRENT_REQUESTS=5
      - MODEL_CACHE_SIZE=1000
      - ENABLE_GPU=true
    
    # 卷挂载
    volumes:
      # 模型文件持久化
      - ./models:/app/models
      # 缓存目录
      - ./cache:/app/cache
      # 日志目录
      - ./logs:/app/logs
      # 临时文件目录
      - ./temp:/app/temp
      # 配置文件
      - ./config:/app/config
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 资源限制
    mem_limit: 8g
    mem_reservation: 4g
    cpus: '4.0'
    
    # 网络配置
    networks:
      - face-analyzer-network
    
    # 依赖服务
    # depends_on:
    #   - redis
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 用户配置（可选，提高安全性）
    # user: "1000:1000"

  # Redis缓存服务（可选）
  # redis:
  #   image: redis:7-alpine
  #   container_name: face-analyzer-redis
  #   restart: unless-stopped
  #
  #   ports:
  #     - "6379:6379"
  #
  #   volumes:
  #     - redis-data:/data
  #     - ./redis.conf:/usr/local/etc/redis/redis.conf
  #
  #   command: redis-server /usr/local/etc/redis/redis.conf
  #
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #
  #   networks:
  #     - face-analyzer-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: face-analyzer-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    
    depends_on:
      - face-analyzer
    
    networks:
      - face-analyzer-network

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: face-analyzer-prometheus
    restart: unless-stopped
    
    ports:
      - "9090:9090"
    
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    networks:
      - face-analyzer-network

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: face-analyzer-grafana
    restart: unless-stopped
    
    ports:
      - "3000:3000"
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    
    networks:
      - face-analyzer-network

# 网络配置
networks:
  face-analyzer-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷配置
volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
