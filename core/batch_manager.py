#!/usr/bin/env python3
"""
批处理管理器模块
"""

import os
from tqdm import tqdm

from core.face_processor import process_video_with_segmentation
from utils.file_service import (
    collect_video_files, ensure_directory_exists, get_output_path
)
from utils.logging_service import ResultStatus
from task import (
    FaceAnalyzerCacheManager, process_with_cache,
    generate_single_webp, generate_webp_animation, generate_video_preview
)
from config.settings import Config

from loguru import logger


class BatchManager:
    """批处理管理器类 - 负责批量任务的管理和协调"""

    def __init__(self):
        """
        初始化批处理管理器
        """

        # 初始化缓存管理器（根据统一配置）
        if Config.CACHE_ENABLED:
            self.cache_manager = FaceAnalyzerCacheManager()
        else:
            self.cache_manager = None

    def process_videos(self, input_base: str, output_base: str):
        """
        批量处理视频文件

        Args:
            input_base (str): 输入基础路径
            output_base (str): 输出基础路径
        """
        # 初始化服务
        result_status = ResultStatus(output_base)

        # 收集视频文件
        video_paths = collect_video_files(input_base)

        # 确保输出目录存在
        ensure_directory_exists(output_base)

        # 加载已处理的文件
        processed_set = result_status.get_processed()

        # 过滤掉已处理的视频
        pending_paths = [p for p in video_paths if p not in processed_set]

        # 处理视频
        for input_path in tqdm(pending_paths, desc="Processing videos with geometry validation", unit="file"):
            # 获取输出路径和基础文件名
            output_dir, base_filename = get_output_path(input_path, input_base, output_base)
            ensure_directory_exists(output_dir)

            # === 缓存检查逻辑（FaceProcessor 调用前） ===
            cached_result = None
            if self.cache_manager:
                cached_result = self.cache_manager.get_cached_result(input_path)

            if cached_result:
                # 使用缓存结果，跳过 FaceProcessor
                result = process_with_cache(input_path, cached_result)
            else:
                # 使用FaceProcessor处理单个视频
                result = process_video_with_segmentation(input_path)

                # === 缓存写入逻辑（FaceProcessor 调用后） ===
                if self.cache_manager and result.success:
                    # 直接缓存按得分分组的帧索引
                    self.cache_manager.cache_result(input_path, result.valid_frames)

            # 检查处理是否成功
            if not result.success:
                logger.error(f"视频处理失败: {input_path}")
                error_message = ""
                if hasattr(result, 'error') and result.error:
                    error_message = result.error
                    logger.error(f"错误详情: {result.error}")
                result_status.log_error(input_path, error_message)
                continue

            # 生成单个WebP
            if Config.WEBP_SINGLE_ENABLED:
                webp_single_path = os.path.join(output_dir, base_filename + Config.WEBP_OUTPUT_SINGLE_SUFFIX + ".webp")
                generate_single_webp(input_path, result, webp_single_path)

            # 生成WebP动画
            if Config.WEBP_ANIMATION_ENABLED:
                webp_anim_path = os.path.join(output_dir, base_filename + Config.WEBP_OUTPUT_ANIMATION_SUFFIX + ".webp")
                generate_webp_animation(input_path, result, webp_anim_path)

            # === 视频预览生成逻辑（位于WebP生成之后） ===
            if result.valid_frames and Config.VIDEO_PREVIEW_ENABLED:
                video_preview_path = os.path.join(output_dir, base_filename + Config.VIDEO_PREVIEW_OUTPUT_SUFFIX + "." + Config.VIDEO_PREVIEW_OUTPUT_FORMAT)
                generate_video_preview(input_path, result, video_preview_path)

            # 标记为已处理
            result_status.log_processed(input_path)

            # 记录结果
            result_status.log_success(input_path)

            if result.valid_frames:
                # 获取最高得分的帧索引用于显示
                highest_score = max(result.valid_frames.keys())
                frame_indices = result.valid_frames[highest_score]
                cache_info = " (cached)" if cached_result else ""
                logger.info(f"找到有效的帧索引：{frame_indices}{cache_info}")
            else:
                logger.warning(f"未找到有效几何形状")
            

