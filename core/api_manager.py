#!/usr/bin/env python3
"""
API管理器模块
定义Flask应用的所有路由和端点
专注于HTTP层面的处理：路由定义、请求解析、响应格式化
"""

from flask import Flask, request, jsonify

from task.cache_manager import FaceAnalyzerCacheManager
from core.api_helper import analyze_and_generate_webp_animation, analyze_and_generate_single_webp
from utils.concurrency_control import ConcurrencyController, concurrency_limit
from config.settings import Config

from loguru import logger

def create_app() -> Flask:
    """
    创建Flask应用实例

    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)

    # 初始化核心组件
    cache_manager = FaceAnalyzerCacheManager()

    # 初始化并发控制器
    concurrency_controller = ConcurrencyController(Config.CONCURRENCY_LIMIT)

    @app.route('/analyze', methods=['POST'])
    @concurrency_limit(concurrency_controller)
    def analyze_video():
        """视频分析API端点"""
        # 解析和验证JSON请求
        data = request.get_json()

        input_path = data.get('input_path')
        output_path = data.get('output_path')
        resolution = data.get('resolution')

        # 记录请求开始
        log_msg = f"开始处理请求: {input_path} -> {output_path}"
        if resolution:
            log_msg += f", 分辨率: {resolution}"
        logger.info(log_msg)

        # 调用业务逻辑
        result = analyze_and_generate_webp_animation(
            cache_manager, input_path, output_path, resolution
        )

        if result.get("success", True):
            logger.info(f"处理完成: {result}")
            return jsonify(result)
        else:
            logger.error(f"处理失败: {result}")
            return jsonify(result), 500

    @app.route('/analyze_frame', methods=['POST'])
    @concurrency_limit(concurrency_controller)
    def analyze_video_frame():
        """视频单帧分析API端点"""
        # 解析和验证JSON请求
        data = request.get_json()

        input_path = data.get('input_path')
        output_path = data.get('output_path')
        resolution = data.get('resolution')

        # 记录请求开始
        log_msg = f"开始处理单帧请求: {input_path} -> {output_path}"
        if resolution:
            log_msg += f", 分辨率: {resolution}"
        logger.info(log_msg)

        # 调用业务逻辑
        result = analyze_and_generate_single_webp(
            cache_manager, input_path, output_path, resolution
        )

        if result.get("success", True):
            logger.info(f"单帧处理完成: {result}")
            return jsonify(result)
        else:
            logger.error(f"单帧处理失败: {result}")
            return jsonify(result), 500

    return app
