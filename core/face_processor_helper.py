#!/usr/bin/env python3
"""
FaceProcessor辅助工具模块

包含从FaceProcessor类中重构出来的辅助函数：
- 调试可视化功能
- CUDA错误处理功能
"""

import os
import re
import time
import cv2
import torch
from loguru import logger
from config.settings import Config


def init_debug_visualization():
    """
    初始化调试可视化功能
    
    创建调试输出目录并记录日志
    """
    if Config.DEBUG_FACE_VISUALIZATION:
        # 创建调试输出目录
        os.makedirs(Config.DEBUG_OUTPUT_DIR, exist_ok=True)
        logger.info(f"调试可视化已启用，输出目录: {Config.DEBUG_OUTPUT_DIR}")


def save_debug_visualization(frame, face_results, frame_number):
    """
    保存调试可视化图像
    
    Args:
        frame: 原始图像帧
        face_results: 人脸检测结果列表
        frame_number: 帧号
    """
    if not Config.DEBUG_FACE_VISUALIZATION or not face_results:
        return

    try:
        # 复制图像以避免修改原始数据
        debug_frame = frame.copy()

        # 绘制人脸检测结果
        for face_result in face_results:
            # 获取检测框坐标
            bbox = face_result.bbox
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])

            # 绘制检测框
            cv2.rectangle(debug_frame, (x1, y1), (x2, y2),
                        (0, 255, 0), 2)  # 绿色边框，线条粗细2

            # 绘制置信度得分
            confidence = face_result.confidence
            score_text = f"{confidence:.3f}"
            text_size = cv2.getTextSize(score_text, cv2.FONT_HERSHEY_SIMPLEX,
                                      0.6, 2)[0]  # 文本缩放0.6，线条粗细2

            # 计算文本位置（检测框上方）
            text_x = x1
            text_y = y1 - 10 if y1 - 10 > text_size[1] else y1 + text_size[1] + 10

            # 绘制文本背景
            cv2.rectangle(debug_frame,
                        (text_x, text_y - text_size[1] - 5),
                        (text_x + text_size[0] + 5, text_y + 5),
                        (0, 0, 0), -1)

            # 绘制置信度文本
            cv2.putText(debug_frame, score_text, (text_x + 2, text_y - 2),
                      cv2.FONT_HERSHEY_SIMPLEX, 0.6,
                      (0, 255, 255), 2)  # 黄色文本，缩放0.6，线条粗细2

            # 绘制人脸关键点（5个关键点）
            if hasattr(face_result, 'landmarks') and face_result.landmarks is not None:
                landmarks = face_result.landmarks.reshape(-1, 2)
                for landmark in landmarks:
                    landmark_x, landmark_y = int(landmark[0]), int(landmark[1])
                    cv2.circle(debug_frame, (landmark_x, landmark_y),
                             3, (255, 0, 0), -1)  # 蓝色关键点，半径3

        # 保存调试图像
        debug_filename = f"face_frame_{frame_number:06d}.jpg"
        debug_filepath = os.path.join(Config.DEBUG_OUTPUT_DIR, debug_filename)

        # 使用高质量保存
        cv2.imwrite(debug_filepath, debug_frame,
                   [cv2.IMWRITE_JPEG_QUALITY, 95])  # JPEG质量95

        logger.debug(f"调试可视化已保存: {debug_filepath}")

    except Exception as e:
        # 错误处理：不影响主要处理流程
        logger.warning(f"调试可视化保存失败 (帧 {frame_number}): {e}")
        # 不抛出异常，确保主要处理流程继续


def is_cuda_oom_error(error: Exception) -> bool:
    """
    检测是否为CUDA内存不足错误
    
    Args:
        error (Exception): 异常对象
        
    Returns:
        bool: 是否为CUDA OOM错误
    """
    error_str = str(error).lower()
    oom_patterns = [
        r'out of memory',
        r'cuda out of memory',
        r'cuda error: out of memory',
        r'runtime error.*out of memory',
        r'allocation failed',
        r'cuda_error_out_of_memory'
    ]
    
    for pattern in oom_patterns:
        if re.search(pattern, error_str):
            return True
    return False


def handle_cuda_oom_retry(frame_index: int, retry_count: int, error: Exception, thread_id: str) -> bool:
    """
    处理CUDA OOM重试逻辑
    
    Args:
        frame_index (int): 当前帧索引
        retry_count (int): 当前重试次数
        error (Exception): 异常对象
        thread_id (str): 线程标识符
        
    Returns:
        bool: 是否应该继续重试
    """
    if retry_count >= Config.CUDA_OOM_RETRY_COUNT:
        logger.error(f"[Consumer {thread_id}] 帧 {frame_index}: CUDA OOM重试次数已用尽 ({Config.CUDA_OOM_RETRY_COUNT}次)，跳过该帧")
        logger.error(f"[Consumer {thread_id}] 最终错误: {str(error)}")
        return False
    
    logger.warning(f"[Consumer {thread_id}] 帧 {frame_index}: CUDA OOM错误，第 {retry_count + 1}/{Config.CUDA_OOM_RETRY_COUNT} 次重试")
    logger.debug(f"[Consumer {thread_id}] CUDA OOM错误详情: {str(error)}")
    
    # 清理CUDA缓存
    try:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.debug(f"[Consumer {thread_id}] CUDA缓存已清理")
    except Exception as cache_error:
        logger.warning(f"[Consumer {thread_id}] CUDA缓存清理失败: {str(cache_error)}")
    
    # 等待指定时间
    logger.debug(f"[Consumer {thread_id}] 等待 {Config.CUDA_OOM_RETRY_DELAY} 秒后重试...")
    time.sleep(Config.CUDA_OOM_RETRY_DELAY)
    
    return True
