#!/usr/bin/env python3
"""
人脸处理器模块
"""

import sys
import os
import threading
import time
import queue
import re
import cv2
import numpy as np
import torch


# 添加项目根目录到Python路径以便导入models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.retinaface.detection import RetinaFaceDetection
from module.video_processors import create_video_processor
from classes.process_result import ProcessResult
from classes.frame_processing_result import FrameProcessingResult, STOP_SIGNAL
from classes.retinaface_result import RetinaFaceResult
from module.face_filter_chain import create_face_filter_chain
from module.frame_filter_chain import create_frame_filter_chain

from utils.image_utils import crop_frame_to_aspect_ratio
from config.settings import Config
from utils.video_segmentation import process_long_video, get_video_duration
from utils.video_processor_context import safe_video_processing

# 创建日志记录器
from loguru import logger


# 全局detector已移除，现在在FaceProcessor类中初始化


def process_video_with_segmentation(video_path: str) -> ProcessResult:
    """
    处理视频文件，对长视频（>5分钟）进行分割并行处理

    Args:
        face_processor: 人脸处理器实例
        video_path (str): 视频文件路径

    Returns:
        ProcessResult: 处理结果
    """
    face_processor = FaceProcessor()
    # 1. 检测视频时长
    duration = get_video_duration(video_path)

    # 2. 判断是否需要分割
    if duration <= 60 or Config.VIDEO_SPLIT_SEGMENTS <= 1:
        return face_processor.process_single_video(video_path, "single")

    # 3. 长视频分割并行处理
    return process_long_video(face_processor, video_path, duration)

class FaceProcessor:
    """人脸处理器类 - 负责单个视频文件的完整处理流程"""

    def __init__(self):
        """
        初始化人脸处理器
        """
        # 初始化检测器和验证器
        self.detector = RetinaFaceDetection(Config.RETINAFACE_MODEL_PATH, device=Config.DEVICE)

        # 初始化处理组件
        self.video_processor = create_video_processor(Config.VIDEO_PROCESSOR_TYPE)

        # 初始化帧过滤器链
        self.frame_filter_chain = create_frame_filter_chain()

        # 初始化人脸过滤链
        self.face_filter_chain = create_face_filter_chain()

        # 初始化调试可视化
        self._init_debug_visualization()

    def release_resources(self):
        """释放所有资源"""
        try:
            if hasattr(self, 'video_processor') and self.video_processor:
                self.video_processor.release_resources()
        except Exception as e:
            logger.error(f"释放video_processor资源失败: {e}")

    def __del__(self):
        """析构函数，确保资源被释放"""
        try:
            self.release_resources()
        except Exception:
            # 在析构函数中忽略异常
            pass

    def _init_debug_visualization(self):
        """初始化调试可视化功能"""
        if Config.DEBUG_FACE_VISUALIZATION:
            # 创建调试输出目录
            os.makedirs(Config.DEBUG_OUTPUT_DIR, exist_ok=True)
            logger.info(f"调试可视化已启用，输出目录: {Config.DEBUG_OUTPUT_DIR}")

    def _save_debug_visualization(self, frame, face_results, frame_number):
        """
        保存调试可视化图像

        Args:
            frame: 原始图像帧
            face_results: 人脸检测结果
            frame_number: 帧号
        """
        if not Config.DEBUG_FACE_VISUALIZATION or not face_results:
            return

        try:
            # 复制图像以避免修改原始数据
            debug_frame = frame.copy()

            # 绘制人脸检测结果
            for face_result in face_results:
                # 获取检测框坐标
                bbox = face_result.bbox
                x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])

                # 绘制检测框
                cv2.rectangle(debug_frame, (x1, y1), (x2, y2),
                            (0, 255, 0), 2)  # 绿色边框，线条粗细2

                # 绘制置信度得分
                confidence = face_result.confidence
                score_text = f"{confidence:.3f}"
                text_size = cv2.getTextSize(score_text, cv2.FONT_HERSHEY_SIMPLEX,
                                          0.6, 2)[0]  # 文本缩放0.6，线条粗细2

                # 计算文本位置（检测框上方）
                text_x = x1
                text_y = y1 - 10 if y1 - 10 > text_size[1] else y1 + text_size[1] + 10

                # 绘制文本背景
                cv2.rectangle(debug_frame,
                            (text_x, text_y - text_size[1] - 5),
                            (text_x + text_size[0] + 5, text_y + 5),
                            (0, 0, 0), -1)

                # 绘制置信度文本
                cv2.putText(debug_frame, score_text, (text_x + 2, text_y - 2),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6,
                          (0, 255, 255), 2)  # 黄色文本，缩放0.6，线条粗细2

                # 绘制人脸关键点（5个关键点）
                if hasattr(face_result, 'landmarks') and face_result.landmarks is not None:
                    landmarks = face_result.landmarks.reshape(-1, 2)
                    for landmark in landmarks:
                        landmark_x, landmark_y = int(landmark[0]), int(landmark[1])
                        cv2.circle(debug_frame, (landmark_x, landmark_y),
                                 3, (255, 0, 0), -1)  # 蓝色关键点，半径3

            # 保存调试图像
            debug_filename = f"face_frame_{frame_number:06d}.jpg"
            debug_filepath = os.path.join(Config.DEBUG_OUTPUT_DIR, debug_filename)

            # 使用高质量保存
            cv2.imwrite(debug_filepath, debug_frame,
                       [cv2.IMWRITE_JPEG_QUALITY, 95])  # JPEG质量95

            logger.debug(f"调试可视化已保存: {debug_filepath}")

        except Exception as e:
            # 错误处理：不影响主要处理流程
            logger.warning(f"调试可视化保存失败 (帧 {frame_number}): {e}")
            # 不抛出异常，确保主要处理流程继续

    def detect_faces(self, frame):
        """
        检测人脸并转换为RetinaFaceResult实例列表

        Args:
            frame: 输入帧

        Returns:
            List[RetinaFaceResult]: RetinaFaceResult实例列表
        """
        input_data = {'img': frame}
        detection_result = self.detector(input_data)

        # 将检测结果转换为RetinaFaceResult实例列表
        face_results = []

        if detection_result is not None and len(detection_result) == 2:
            dets, landms = detection_result

            if dets is not None and len(dets) > 0:
                for i in range(len(dets)):
                    det = dets[i]
                    if len(det) < 5:
                        continue

                    x1, y1, x2, y2, confidence = det[:5]

                    # 获取对应的关键点
                    landmarks = None
                    if landms is not None and i < len(landms):
                        landmarks = landms[i]

                    if landmarks is not None:
                        # 创建RetinaFaceResult实例
                        face_result = RetinaFaceResult(
                            face_id=i + 1,
                            confidence=float(confidence),
                            bbox=[float(x1), float(y1), float(x2), float(y2)],
                            landmarks=landmarks
                        )
                        face_results.append(face_result)

        return face_results

    def _is_cuda_oom_error(self, error: Exception) -> bool:
        """
        检测是否为CUDA内存不足错误

        Args:
            error (Exception): 异常对象

        Returns:
            bool: 是否为CUDA OOM错误
        """
        error_str = str(error).lower()
        oom_patterns = [
            r'out of memory',
            r'cuda out of memory',
            r'cuda error: out of memory',
            r'runtime error.*out of memory',
            r'allocation failed',
            r'cuda_error_out_of_memory'
        ]

        for pattern in oom_patterns:
            if re.search(pattern, error_str):
                return True
        return False

    def _handle_cuda_oom_retry(self, frame_index: int, retry_count: int, error: Exception, thread_id: str) -> bool:
        """
        处理CUDA OOM重试逻辑

        Args:
            frame_index (int): 当前帧索引
            retry_count (int): 当前重试次数
            error (Exception): 异常对象
            thread_id (str): 线程标识符

        Returns:
            bool: 是否应该继续重试
        """
        if retry_count >= Config.CUDA_OOM_RETRY_COUNT:
            logger.error(f"[Consumer {thread_id}] 帧 {frame_index}: CUDA OOM重试次数已用尽 ({Config.CUDA_OOM_RETRY_COUNT}次)，跳过该帧")
            logger.error(f"[Consumer {thread_id}] 最终错误: {str(error)}")
            return False

        logger.warning(f"[Consumer {thread_id}] 帧 {frame_index}: CUDA OOM错误，第 {retry_count + 1}/{Config.CUDA_OOM_RETRY_COUNT} 次重试")
        logger.debug(f"[Consumer {thread_id}] CUDA OOM错误详情: {str(error)}")

        # 清理CUDA缓存
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                logger.debug(f"[Consumer {thread_id}] CUDA缓存已清理")
        except Exception as cache_error:
            logger.warning(f"[Consumer {thread_id}] CUDA缓存清理失败: {str(cache_error)}")

        # 等待指定时间
        logger.debug(f"[Consumer {thread_id}] 等待 {Config.CUDA_OOM_RETRY_DELAY} 秒后重试...")
        time.sleep(Config.CUDA_OOM_RETRY_DELAY)

        return True

    def process_single_video(self, video_path: str, thread_id: str = "main") -> ProcessResult:
        """
        处理单个视频文件的完整流程

        Args:
            video_path (str): 视频文件路径
            thread_id (str): 线程ID，用于日志标识

        Returns:
            ProcessResult: 处理结果
        """
        # 创建 FrameProcessingResult 实例
        processing_result = FrameProcessingResult()

        try:
            # 调用新的 find_valid_frame 方法
            self.find_valid_frame(video_path, processing_result, search_all=Config.SEARCH_ALL, thread_id=thread_id)
        except Exception as e:
            logger.error(f"视频处理异常: {video_path}, 错误: {str(e)}")
            return ProcessResult(
                success=False,
                error=str(e)
            )

        # 使用 _build_result 直接构造并返回 ProcessResult
        return ProcessResult(
            success=True,
            video_path=video_path,
            valid_frames=dict(processing_result.valid_frames)
        )

    def find_valid_frame(self, video_path: str, processing_result: FrameProcessingResult, search_all: bool = False, thread_id: str = "main") -> None:
        """
        在视频中找到有效几何形状的人脸（支持单帧和全搜索模式）

        Args:
            video_path (str): 视频文件路径
            processing_result (FrameProcessingResult): 处理结果对象，结果将直接写入此对象
            search_all (bool): 搜索模式
                - False: 找到第一个有效帧即退出（默认）
                - True: 完整搜索整个视频中的所有有效帧
            thread_id (str): 线程标识符

        Returns:
            None: 结果直接写入 processing_result 对象
        """
        producer_id = f"{thread_id}_producer"
        consumer_id = f"{thread_id}_consumer"

        # 创建生产者和消费者线程
        producer_thread = threading.Thread(
            target=self._frame_producer,
            args=(video_path, processing_result, producer_id)
        )
        consumer_thread = threading.Thread(
            target=self._frame_consumer,
            args=(processing_result, search_all, consumer_id)
        )

        # 启动线程
        producer_thread.start()
        consumer_thread.start()

        # 等待线程完成
        producer_thread.join()
        consumer_thread.join()

    def _frame_producer(self, video_path: str, processing_result: FrameProcessingResult, thread_id: str):
        """
        生产者线程：负责视频帧读取和预处理

        Args:
            video_path (str): 视频文件路径
            processing_result (FrameProcessingResult): 处理结果对象
            thread_id (str): 线程标识符
        """
        # 使用上下文管理器确保资源正确释放
        with safe_video_processing(self.video_processor, video_path) as video_iterator:
            for frame, frame_info in video_iterator:
                # 检查停止信号
                if processing_result.stop_event.is_set():
                    break

                # 帧过滤预处理
                is_valid, _ = self.frame_filter_chain(frame, frame_info)

                if not is_valid:
                    continue

                # 队列写入
                frame_data = {
                    'frame': frame,
                    'frame_info': frame_info,
                    'is_stop_signal': False
                }

                # 非阻塞队列写入操作 + 重试机制
                max_retries = 10
                retry_count = 0
                write_success = False

                while retry_count < max_retries:
                    try:
                        # 非阻塞写入
                        processing_result.frame_queue.put_nowait(frame_data)
                        write_success = True
                        break  # 成功写入，退出重试循环
                    except queue.Full:
                        # 检查停止信号
                        if processing_result.stop_event.is_set():
                            return  # 立即退出生产者线程

                        retry_count += 1
                        time.sleep(1.0)  # 等待1秒后重试

                if not write_success:
                    # 重试失败处理
                    logger.warning(f"[Producer Debug] {thread_id} - 队列写入失败，跳过帧 {frame_info['global_idx']}，重试次数: {max_retries}")
                    continue  # 跳过当前帧，继续处理下一帧

            # 发送停止信号 - 非阻塞写入 + 重试机制
            max_retries = 10
            retry_count = 0
            stop_signal_sent = False

            while retry_count < max_retries:
                try:
                    # 非阻塞写入停止信号
                    processing_result.frame_queue.put_nowait(STOP_SIGNAL)
                    stop_signal_sent = True
                    break  # 成功写入，退出重试循环
                except queue.Full:
                    retry_count += 1
                    time.sleep(1.0)  # 等待1秒后重试

            if not stop_signal_sent:
                # 停止信号发送失败
                logger.error(f"[Producer Debug] {thread_id} - 停止信号发送失败，重试次数: {max_retries}")

    def _frame_consumer(self, processing_result: FrameProcessingResult, search_all: bool, thread_id: str):
        """
        消费者线程：负责人脸检测和过滤处理（带智能错误处理）

        Args:
            processing_result (FrameProcessingResult): 处理结果对象
            search_all (bool): 是否搜索所有帧
            thread_id (str): 线程标识符
        """
        while True:
            frame_data = processing_result.frame_queue.get()

            # 检查停止信号
            if frame_data['is_stop_signal']:
                break

            # 处理帧数据 - 带智能错误处理
            frame_index = frame_data['frame_info']['global_idx']

            # 使用智能错误处理机制处理单帧
            success = self._process_single_frame_with_retry(
                frame_data, processing_result, search_all, thread_id
            )

            # 如果处理成功且触发早期退出，则退出循环
            if success and processing_result.stop_event.is_set():
                break

    def _process_single_frame_with_retry(self, frame_data: dict, processing_result: FrameProcessingResult,
                                       search_all: bool, thread_id: str) -> bool:
        """
        带重试机制的单帧处理

        Args:
            frame_data (dict): 帧数据
            processing_result (FrameProcessingResult): 处理结果对象
            search_all (bool): 是否搜索所有帧
            thread_id (str): 线程标识符

        Returns:
            bool: 处理是否成功
        """
        frame_index = frame_data['frame_info']['global_idx']
        retry_count = 0

        while retry_count <= Config.CUDA_OOM_RETRY_COUNT:
            try:
                # 图像裁剪处理 - 裁剪为正方形（1:1宽高比）
                cropped_frame = crop_frame_to_aspect_ratio(frame_data['frame'], 1.0)

                # 人脸检测（已包含转换逻辑）
                face_results = self.detect_faces(cropped_frame)

                # 调试可视化：保存检测结果
                self._save_debug_visualization(cropped_frame, face_results, frame_index)

                if face_results:
                    # 使用人脸过滤链进行过滤
                    highest_score, _ = self.face_filter_chain(cropped_frame, face_results)

                    # 线程安全地添加有效帧（按得分分组）
                    with processing_result.lock:
                        processing_result.valid_frames[highest_score].append(frame_index)

                        # 早期退出逻辑（仅在非全搜索模式）
                        if highest_score >= Config.FACEFILTER_THRESHOLD and not search_all:
                            processing_result.stop_event.set()

                # 处理成功，记录日志（如果有重试）
                if retry_count > 0:
                    logger.info(f"[Consumer {thread_id}] 帧 {frame_index}: 重试成功，共重试 {retry_count} 次")

                return True

            except Exception as e:
                # 检查是否为CUDA OOM错误
                if self._is_cuda_oom_error(e):
                    # CUDA OOM错误，尝试重试
                    if self._handle_cuda_oom_retry(frame_index, retry_count, e, thread_id):
                        retry_count += 1
                        continue
                    else:
                        # 重试次数用尽，跳过该帧
                        return False
                else:
                    # 其他类型错误，直接跳过该帧
                    logger.error(f"[Consumer {thread_id}] 帧 {frame_index}: 处理失败，跳过该帧")
                    logger.error(f"[Consumer {thread_id}] 错误详情: {str(e)}")
                    return False

        # 不应该到达这里
        return False