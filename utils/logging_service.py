#!/usr/bin/env python3
"""
日志服务模块
提供统一的日志配置和管理功能
"""

import os


class ResultStatus:
    """文件状态日志类 - 记录处理成功和失败的文件路径"""

    def __init__(self, output_base: str):
        """
        初始化文件状态日志

        Args:
            output_base (str): 输出基础路径
        """
        from config.settings import Config

        self.output_base = output_base

        # 确保输出目录存在
        if not os.path.exists(output_base):
            os.makedirs(output_base, exist_ok=True)

        self.success_log = os.path.join(output_base, Config.RESULT_SUCCESS_LOG)
        self.error_log = os.path.join(output_base, Config.RESULT_ERROR_LOG)
        self.processed_file = os.path.join(output_base, Config.RESULT_PROCESSED_LOG)

    def log_success(self, file_path: str):
        """
        记录成功处理的文件路径

        Args:
            file_path (str): 成功处理的文件路径
        """
        with open(self.success_log, 'a', encoding='utf-8') as f:
            f.write(file_path + '\n')

    def log_error(self, file_path: str, error_message: str = ""):
        """
        记录失败处理的文件路径和错误信息

        Args:
            file_path (str): 失败处理的文件路径
            error_message (str): 错误信息
        """
        with open(self.error_log, 'a', encoding='utf-8') as f:
            f.write(file_path + '\n')
            f.write(error_message + '\n')

    def get_processed(self):
        """
        读取已处理文件列表

        Returns:
            set: 已处理文件集合
        """
        processed_set = set()
        if os.path.exists(self.processed_file):
            with open(self.processed_file, 'r', encoding='utf-8') as f:
                processed_set = set(line.strip() for line in f if line.strip())
        return processed_set

    def log_processed(self, file_path: str):
        """
        记录文件为已处理

        Args:
            file_path (str): 文件路径
        """
        with open(self.processed_file, 'a', encoding='utf-8') as f:
            f.write(file_path + '\n')


