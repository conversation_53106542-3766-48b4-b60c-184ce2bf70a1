#!/usr/bin/env python3
"""
视频处理器上下文管理器模块

提供确保视频处理器资源正确释放的上下文管理器。
"""

from typing import Generator, Any, Dict, Tuple
from loguru import logger


class VideoProcessorContext:
    """
    视频处理器上下文管理器
    
    确保视频处理器的Generator在使用完毕后正确关闭，
    即使在提前退出（break、异常等）的情况下也能释放资源。
    """
    
    def __init__(self, video_processor, video_path: str):
        """
        初始化上下文管理器
        
        Args:
            video_processor: 视频处理器实例
            video_path: 视频文件路径
        """
        self.video_processor = video_processor
        self.video_path = video_path
        self.generator = None
    
    def __enter__(self) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        进入上下文，创建并返回Generator
        
        Returns:
            Generator: 视频帧生成器
        """
        self.generator = self.video_processor(self.video_path)
        return self.generator
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        退出上下文，确保资源被释放
        
        Args:
            exc_type: 异常类型
            exc_val: 异常值
            exc_tb: 异常追踪信息
        """
        try:
            # 关闭Generator
            if self.generator is not None:
                self.generator.close()
        except GeneratorExit:
            # 正常的Generator关闭
            pass
        except Exception as e:
            logger.error(f"关闭视频处理器Generator失败: {e}")
        
        try:
            # 释放视频处理器资源
            if hasattr(self.video_processor, 'release_resources'):
                self.video_processor.release_resources()
        except Exception as e:
            logger.error(f"释放视频处理器资源失败: {e}")


def safe_video_processing(video_processor, video_path: str):
    """
    安全的视频处理函数，确保资源正确释放
    
    Args:
        video_processor: 视频处理器实例
        video_path: 视频文件路径
        
    Returns:
        VideoProcessorContext: 上下文管理器
        
    Example:
        with safe_video_processing(processor, video_path) as video_iterator:
            for frame, frame_info in video_iterator:
                # 处理帧
                if some_condition:
                    break  # 即使提前退出，资源也会被正确释放
    """
    return VideoProcessorContext(video_processor, video_path)
