#!/usr/bin/env python3
"""
视频分割并行处理模块
"""

import os
import tempfile
import subprocess
import multiprocessing
from typing import List

from classes.process_result import ProcessResult
from config.settings import Config
from loguru import logger




def get_video_duration(video_path: str) -> float:
    """
    获取视频时长（秒）

    Args:
        video_path (str): 视频文件路径

    Returns:
        float: 视频时长（秒）
    """
    cmd = [
        'ffprobe', '-v', 'quiet', '-print_format', 'json',
        '-show_format', video_path
    ]
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)

    import json
    data = json.loads(result.stdout)
    duration = float(data['format']['duration'])
    return duration

def process_long_video(face_processor, video_path: str, duration: float) -> ProcessResult:
    """
    处理长视频：分割为多个片段并行处理

    Args:
        face_processor: 人脸处理器实例
        video_path (str): 视频文件路径
        duration (float): 视频时长（秒）

    Returns:
        ProcessResult: 合并后的处理结果
    """
    segment_duration = duration / Config.VIDEO_SPLIT_SEGMENTS
    logger.info(f"将视频分割为{Config.VIDEO_SPLIT_SEGMENTS}个片段，每段 {segment_duration:.1f}s")

    with tempfile.TemporaryDirectory() as temp_dir:
        logger.debug(f"临时目录: {temp_dir}")

        # 1. 分割视频
        segment_paths = _split_video(video_path, temp_dir, segment_duration)

        # 2. 并行处理
        results = _process_segments_parallel(face_processor, segment_paths)

        # 3. 合并结果
        final_result = _merge_results(results, video_path, segment_duration)

        return final_result

def _split_video(video_path: str, temp_dir: str, segment_duration: float) -> List[str]:
    """
    使用FFmpeg分割视频为多个片段

    Args:
        video_path (str): 原视频路径
        temp_dir (str): 临时目录
        segment_duration (float): 每段时长（秒）

    Returns:
        List[str]: 分割后的视频文件路径列表
    """
    segment_paths = []

    for i in range(Config.VIDEO_SPLIT_SEGMENTS):
        start_time = i * segment_duration
        output_path = os.path.join(temp_dir, f"segment_{i:02d}.mp4")

        cmd = [
            'ffmpeg', '-y', '-v', 'quiet',
            '-ss', str(start_time),
            '-t', str(segment_duration),
            '-i', video_path,
            '-c', 'copy',  # 使用流复制，避免重新编码
            '-avoid_negative_ts', 'make_zero',
            output_path
        ]

        subprocess.run(cmd, check=True)
        segment_paths.append(output_path)
        logger.info(f"片段 {i+1}/{Config.VIDEO_SPLIT_SEGMENTS} 分割完成: {os.path.basename(output_path)}")

    logger.info(f"成功分割 {len(segment_paths)}/{Config.VIDEO_SPLIT_SEGMENTS} 个片段")
    return segment_paths

def _process_segments_parallel(face_processor, segment_paths: List[str]) -> List[ProcessResult]:
    """
    并行处理视频片段

    Args:
        segment_paths (List[str]): 视频片段路径列表

    Returns:
        List[ProcessResult]: 处理结果列表
    """
    if not segment_paths:
        return []

    # 设置multiprocessing启动方法为spawn以避免CUDA问题
    multiprocessing.set_start_method('spawn', force=True)

    # 创建进程池，最大进程数由配置决定
    max_workers = Config.VIDEO_SPLIT_SEGMENTS
    logger.info(f"启动 {max_workers} 个并行进程处理 {len(segment_paths)} 个片段")

    # 准备参数：(segment_path, thread_id)
    args_list = [(path, f"seg_{i:02d}") for i, path in enumerate(segment_paths)]

    with multiprocessing.Pool(processes=max_workers) as pool:
        results = pool.starmap(face_processor.process_single_video, args_list)

    successful_results = [r for r in results if r.success]
    failed_count = len(results) - len(successful_results)

    logger.info(f"并行处理完成: {len(successful_results)} 成功, {failed_count} 失败")

    return results

def _merge_results(results: List[ProcessResult], original_video_path: str,
                  segment_duration: float) -> ProcessResult:
    """
    合并多个片段的处理结果

    Args:
        results (List[ProcessResult]): 片段处理结果列表
        original_video_path (str): 原始视频路径
        segment_duration (float): 每段时长（秒）

    Returns:
        ProcessResult: 合并后的最终结果
    """
    if not results:
        return ProcessResult(
            success=False,
            video_path=original_video_path,
            valid_frames={}
        )

    # 统计成功的结果
    successful_results = [r for r in results if r.success]

    if not successful_results:
        return ProcessResult(
            success=False,
            video_path=original_video_path,
            error="没有可以合并的片段"
        )

    logger.info(f"合并 {len(successful_results)}/{len(results)} 个成功的片段结果")

    # 合并 valid_frames，调整帧索引
    merged_valid_frames = {}

    # 获取原视频的FPS来计算帧偏移
    fps = _get_video_fps(original_video_path)

    for i, result in enumerate(successful_results):

        # 计算这个片段在原视频中的帧偏移量
        segment_start_time = i * segment_duration
        frame_offset = int(segment_start_time * fps)

        logger.debug(f"片段 {i}: 时间偏移 {segment_start_time:.1f}s, 帧偏移 {frame_offset}")

        # 调整帧索引并合并
        for score, frame_indices in result.valid_frames.items():
            if score not in merged_valid_frames:
                merged_valid_frames[score] = []

            # 调整帧索引
            adjusted_indices = [idx + frame_offset for idx in frame_indices]
            merged_valid_frames[score].extend(adjusted_indices)

    # 对每个得分组的帧索引进行排序和去重
    for score in merged_valid_frames:
        merged_valid_frames[score] = sorted(list(set(merged_valid_frames[score])))

    # 统计结果
    total_frames = sum(len(indices) for indices in merged_valid_frames.values())
    logger.info(f"合并结果: 总共 {total_frames} 个有效帧")
    for score, indices in merged_valid_frames.items():
        logger.debug(f"  得分 {score}: {len(indices)} 个帧")

    return ProcessResult(
        success=True,
        video_path=original_video_path,
        valid_frames=merged_valid_frames
    )

def _get_video_fps(video_path: str) -> float:
    """
    获取视频帧率

    Args:
        video_path (str): 视频文件路径

    Returns:
        float: 视频帧率
    """
    cmd = [
        'ffprobe', '-v', 'quiet', '-print_format', 'json',
        '-show_streams', '-select_streams', 'v:0', video_path
    ]
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)

    import json
    data = json.loads(result.stdout)

    # 获取视频流的帧率
    for stream in data.get('streams', []):
        if stream.get('codec_type') == 'video':
            fps_str = stream.get('r_frame_rate', '25/1')
            # 处理分数形式的帧率，如 "25/1"
            if '/' in fps_str:
                num, den = fps_str.split('/')
                fps = float(num) / float(den)
            else:
                fps = float(fps_str)
            return fps

    return 25.0