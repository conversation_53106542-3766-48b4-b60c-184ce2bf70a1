#!/usr/bin/env python3
"""
图像处理工具模块
"""

import cv2
import numpy as np
import logging
from typing import Tuple

logger = logging.getLogger(__name__)


def parse_resolution(resolution: str) -> Tuple[int, int]:
    """
    解析分辨率字符串

    Args:
        resolution: 分辨率字符串，格式如 "640x480" 或 "1920x1080"

    Returns:
        tuple: (宽度, 高度)

    Raises:
        ValueError: 如果分辨率格式无效
    """
    try:
        parts = resolution.lower().split('x')
        if len(parts) != 2:
            raise ValueError("分辨率格式应为 'WIDTHxHEIGHT'")

        width = int(parts[0])
        height = int(parts[1])

        if width <= 0 or height <= 0:
            raise ValueError("宽度和高度必须大于0")

        if width > 7680 or height > 4320:  # 8K限制
            raise ValueError("分辨率不能超过8K (7680x4320)")

        return width, height
    except (ValueError, IndexError) as e:
        raise ValueError(f"无效的分辨率格式 '{resolution}': {e}")


def crop_frame_to_aspect_ratio(frame: np.ndarray, target_aspect_ratio: float) -> np.ndarray:
    """
    将帧裁剪到指定的宽高比（中心裁剪）

    Args:
        frame: 输入帧
        target_aspect_ratio: 目标宽高比

    Returns:
        np.ndarray: 裁剪后的帧
    """
    current_height, current_width = frame.shape[:2]
    current_aspect_ratio = current_width / current_height

    # 如果宽高比已经匹配，直接返回
    if abs(current_aspect_ratio - target_aspect_ratio) < 0.001:
        return frame

    if current_aspect_ratio > target_aspect_ratio:
        # 当前帧更宽，需要裁剪宽度
        new_width = int(current_height * target_aspect_ratio)
        new_height = current_height
        x_offset = (current_width - new_width) // 2
        y_offset = 0
    else:
        # 当前帧更高，需要裁剪高度
        new_width = current_width
        new_height = int(current_width / target_aspect_ratio)
        x_offset = 0
        y_offset = (current_height - new_height) // 2

    # 执行中心裁剪
    cropped_frame = frame[y_offset:y_offset + new_height, x_offset:x_offset + new_width]

    return cropped_frame


def resize_frame(frame: np.ndarray, target_width: int, target_height: int) -> np.ndarray:
    """
    调整帧的大小

    Args:
        frame: 输入帧
        target_width: 目标宽度
        target_height: 目标高度

    Returns:
        np.ndarray: 调整大小后的帧
    """
    current_height, current_width = frame.shape[:2]

    # 如果目标尺寸与当前尺寸相同，直接返回
    if current_width == target_width and current_height == target_height:
        return frame

    # 使用高质量的插值方法
    if target_width * target_height > current_width * current_height:
        # 放大使用CUBIC插值
        interpolation = cv2.INTER_CUBIC
    else:
        # 缩小使用AREA插值
        interpolation = cv2.INTER_AREA

    resized_frame = cv2.resize(frame, (target_width, target_height), interpolation=interpolation)

    return resized_frame


def resize_frame_with_aspect_ratio(frame: np.ndarray, target_width: int, target_height: int) -> np.ndarray:
    """
    调整帧的大小，如果宽高比不匹配则先进行中心裁剪

    Args:
        frame: 输入帧
        target_width: 目标宽度
        target_height: 目标高度

    Returns:
        np.ndarray: 处理后的帧
    """
    current_height, current_width = frame.shape[:2]

    # 如果目标尺寸与当前尺寸相同，直接返回
    if current_width == target_width and current_height == target_height:
        return frame

    # 计算目标宽高比
    target_aspect_ratio = target_width / target_height

    # 先进行中心裁剪以匹配宽高比
    cropped_frame = crop_frame_to_aspect_ratio(frame, target_aspect_ratio)

    # 再进行缩放到目标尺寸
    final_frame = resize_frame(cropped_frame, target_width, target_height)

    return final_frame

