#!/usr/bin/env python3
"""
帧处理结果通信类
"""

import queue
import threading
from collections import defaultdict
from typing import Dict, List


class FrameProcessingResult:
    """帧处理结果通信类"""

    def __init__(self):
        self.frame_queue = queue.Queue(maxsize=100)  # 帧数据队列
        self.stop_event = threading.Event()        # 停止事件
        self.valid_frames: Dict[float, List[int]] = defaultdict(list)  # 按得分分组的有效帧字典
        self.lock = threading.Lock()               # 线程锁


# 停止信号常量
STOP_SIGNAL = {'is_stop_signal': True, 'frame': None, 'frame_info': None}
