#!/usr/bin/env python3
"""
处理结果数据类
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional
from collections import defaultdict


@dataclass
class ProcessResult:
    """视频处理结果类"""

    # 核心字段
    success: bool
    video_path: str
    valid_frames: Dict[float, List[int]]
    error: Optional[str] = field(default=None)

    def __post_init__(self):
        """初始化后处理，确保 valid_frames 是 defaultdict"""
        if not isinstance(self.valid_frames, defaultdict):
            # 如果传入的是普通字典，转换为 defaultdict
            temp_dict = defaultdict(list)
            temp_dict.update(self.valid_frames)
            self.valid_frames = temp_dict

    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        result = {
            'video_path': self.video_path,
            'success': self.success,
            'valid_frames': dict(self.valid_frames),
        }
        if self.error:
            result['error'] = self.error
        return result
