#!/usr/bin/env python3
"""
人脸过滤器虚基类模块
"""

from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any
import numpy as np

from classes.retinaface_result import RetinaFaceResult


class BaseFaceFilter(ABC):
    """人脸过滤器虚基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """
        初始化过滤器

        Args:
            **kwargs: 过滤器特定的初始化参数
        """
        pass

    @abstractmethod
    def __call__(self, frame: np.ndarray, face_results: List[RetinaFaceResult]) -> Tuple[List[float], Dict[str, Any]]:
        """
        过滤器主要处理方法

        Args:
            frame (np.ndarray): 输入帧
            face_results (List[RetinaFaceResult]): RetinaFace检测结果列表

        Returns:
            Tuple[List[float], Dict[str, Any]]:
                - scores: 每个人脸的得分列表，长度与face_results相同，通过为1.0，不通过为0.0
                - filter_info: 过滤器调试信息字典
        """
        pass



