#!/usr/bin/env python3
"""
评分人脸过滤器模块
"""

from typing import List, Tuple, Dict, Any
import numpy as np

from .base import BaseFaceFilter
from classes.retinaface_result import RetinaFaceResult
from config.settings import Config


class ScoreFaceFilter(BaseFaceFilter):
    """评分人脸过滤器 - 同时进行置信度和尺寸过滤"""

    def __init__(self, **kwargs):
        """
        初始化基础过滤器

        配置参数从 settings.py 中导入，不再通过构造函数传递

        Args:
            **kwargs: 预留的扩展参数
        """
        # 配置参数直接从 settings 模块导入
        self.filter_name = "ScoreFaceFilter"

    def __call__(self, frame: np.ndarray, face_results: List[RetinaFaceResult]) -> Tuple[List[float], Dict[str, Any]]:
        """
        应用基础过滤（置信度 + 尺寸）

        Args:
            frame (np.ndarray): 输入帧
            face_results (List[RetinaFaceResult]): RetinaFace检测结果列表

        Returns:
            Tuple[List[float], Dict[str, Any]]:
                - scores: 每个人脸的得分列表，通过为1.0，不通过为0.0
                - filter_info: 过滤器调试信息字典
        """
        frame_height, frame_width = frame.shape[:2]

        scores = []
        filter_info = {
            'total_faces': len(face_results),
            'confidence_passed': 0,
            'size_passed': 0,
            'ratio_passed': 0,
            'final_passed': 0
        }

        for face_result in face_results:
            # 置信度过滤
            confidence_min_valid = face_result.confidence >= Config.MIN_CONFIDENCE
            confidence_valid = face_result.confidence >= Config.MAX_CONFIDENCE
            if confidence_min_valid:
                filter_info['confidence_passed'] += 1

            # 尺寸过滤
            size_valid = (face_result.width > Config.MIN_FILTER_FACE_SIZE and
                         face_result.height > Config.MIN_FILTER_FACE_SIZE)
            if size_valid:
                filter_info['size_passed'] += 1

            width_ratio = face_result.width / frame_width
            height_ratio = face_result.height / frame_height
            ratio_valid = (width_ratio > Config.MIN_FILTER_FACE_RATIO and
                          height_ratio > Config.MIN_FILTER_FACE_RATIO)
            if ratio_valid:
                filter_info['ratio_passed'] += 1

            # 计算该人脸的得分
            if size_valid and ratio_valid:
                if confidence_valid:
                    # 通过所有过滤条件，得分为1.0
                    scores.append(1.0)
                    filter_info['final_passed'] += 1
                elif confidence_min_valid:
                    scores.append(0.5)
                    filter_info['final_passed'] += 1
                else:
                    # 未通过过滤条件，得分为0.0
                    scores.append(0.0)
            else:
                # 未通过过滤条件，得分为0.0
                scores.append(0.0)

        return scores, filter_info
