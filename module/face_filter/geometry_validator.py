#!/usr/bin/env python3
"""
人脸几何形状验证器模块
"""

import traceback
from typing import List, Tu<PERSON>, Dict, Any
import numpy as np
from shapely.geometry import Point, Polygon
import logging

# 创建日志记录器
logger = logging.getLogger(__name__)

from .base import BaseFaceFilter
from classes.retinaface_result import RetinaFaceResult


class GeometryFaceFilter(BaseFaceFilter):
    """几何形状人脸过滤器 - 基于人脸几何形状进行过滤"""

    def __init__(self, **kwargs):
        """
        初始化几何过滤器

        Args:
            **kwargs: 预留的扩展参数
        """
        self.filter_name = "GeometryFaceFilter"

    def __call__(self, frame: np.ndarray, face_results: List[RetinaFaceResult]) -> Tu<PERSON>[List[float], Dict[str, Any]]:
        """
        应用几何形状过滤

        Args:
            frame (np.ndarray): 输入帧
            face_results (List[RetinaFaceResult]): RetinaFace检测结果列表

        Returns:
            Tuple[List[float], Dict[str, Any]]:
                - scores: 每个人脸的得分列表，通过为1.0，不通过为0.0
                - filter_info: 过滤器调试信息字典
        """
        scores = []
        filter_info = {
            'total_faces': len(face_results),
            'quad_valid_count': 0,
            'nose_inside_count': 0,
            'final_passed': 0
        }

        for face_result in face_results:
            landmarks = face_result.landmarks
            quad_valid, nose_inside = self.validate_face_geometry(landmarks)

            if quad_valid:
                filter_info['quad_valid_count'] += 1
            if nose_inside:
                filter_info['nose_inside_count'] += 1

            # 计算该人脸的得分
            if quad_valid and nose_inside:
                # 通过几何验证，得分为1.0
                scores.append(1.0)
                filter_info['final_passed'] += 1
            elif quad_valid:
                scores.append(0.5)
                filter_info['final_passed'] += 1
            else:
                # 未通过几何验证，得分为0.0
                scores.append(0.0)

        return scores, filter_info


    def validate_face_geometry(self, landmarks):
        """
        验证人脸几何形状是否为有效的四边形，并检查鼻子是否在内部

        Args:
            landmarks: RetinaFace关键点

        Returns:
            tuple: (is_valid_quad, nose_inside)
        """
        if landmarks is None or len(landmarks) < 10:
            return False, False

        # 提取关键点坐标
        left_eye = (landmarks[0], landmarks[1])
        right_eye = (landmarks[2], landmarks[3])
        nose = (landmarks[4], landmarks[5])
        left_mouth = (landmarks[6], landmarks[7])
        right_mouth = (landmarks[8], landmarks[9])

        # 构建四边形的四个顶点
        quad_points = [left_eye, right_eye, right_mouth, left_mouth]

        try:
            # 创建多边形
            polygon = Polygon(quad_points)

            # 检查多边形是否有效
            quad_valid = polygon.is_valid

            if not quad_valid:
                return False, False

            # 检查鼻子是否在四边形内部
            nose_point = Point(nose)
            nose_inside = polygon.contains(nose_point)

            return quad_valid, nose_inside

        except Exception as e:
            error_traceback = traceback.format_exc()
            error_message = f"几何验证错误: {str(e)}\n调用堆栈:\n{error_traceback}"
            logger.error(error_message)
            return False, False


