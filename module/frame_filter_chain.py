#!/usr/bin/env python3
"""
帧过滤器链模块
"""

from typing import Dict, Any, Tu<PERSON>, List
import numpy as np

# 创建日志记录器
from loguru import logger

from .frame_filter.base import BaseFrameFilter
from .frame_filter.quality_filter import QualityFrameFilter
from config.settings import Config


class FrameFilterChain:
    """帧过滤器链，用于组合多个帧过滤器"""
    
    def __init__(self, filters: List[BaseFrameFilter]):
        """
        初始化帧过滤器链
        
        Args:
            filters (List[BaseFrameFilter]): 帧过滤器列表
        """
        self.filters = filters
    
    def __call__(self, frame: np.ndarray, frame_info: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        依次应用所有帧过滤器
        
        Args:
            frame (np.ndarray): 输入帧
            frame_info (Dict[str, Any]): 帧信息字典
            
        Returns:
            Tuple[bool, Dict[str, Any]]:
                - is_valid: 当前帧是否通过所有过滤器
                - filter_info: 所有过滤器的详细信息
        """
        all_filter_info = {}
        overall_valid = True

        for filter_instance in self.filters:
            is_valid, filter_info = filter_instance(frame, frame_info)

            # 记录每个过滤器的信息
            filter_key = filter_instance.filter_name
            all_filter_info[filter_key] = filter_info

            # 如果任何一个过滤器返回False，整体失败
            if not is_valid:
                overall_valid = False
                break

        return overall_valid, all_filter_info


def create_frame_filter_chain() -> FrameFilterChain:
    """
    根据配置创建帧过滤器链

    Returns:
        FrameFilterChain: 配置的帧过滤器链
    """
    # 创建过滤器实例映射
    filter_map = {
        'QualityFilter': QualityFrameFilter()
    }

    # 根据配置创建过滤器列表
    filters = []
    for filter_name in Config.FRAME_FILTER_COMPOSITION:
        if filter_name in filter_map:
            filters.append(filter_map[filter_name])
        else:
            logger.warning(f"未知的帧过滤器类型 '{filter_name}'，已跳过")

    return FrameFilterChain(filters)
