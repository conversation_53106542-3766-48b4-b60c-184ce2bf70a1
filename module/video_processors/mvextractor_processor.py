#!/usr/bin/env python3
"""
基于MVExtractor的视频处理器模块
"""

import os
import cv2
import numpy as np
from typing import Generator, Tuple, Dict, Any
from config.settings import Config
from loguru import logger

try:
    from mvextractor.videocap import VideoCap
    MVEXTRACTOR_AVAILABLE = True
except ImportError:
    MVEXTRACTOR_AVAILABLE = False

from .cv2_processor import CV2VideoProcessor


class MVExtractorVideoProcessor(CV2VideoProcessor):
    """
    基于MVExtractor的视频处理器类，支持运动向量提取
    
    继承自CV2VideoProcessor，复用其视频信息获取和帧读取功能，
    同时提供基于运动向量的智能帧选择能力。
    """

    def __init__(self):
        if not MVEXTRACTOR_AVAILABLE:
            raise ImportError("MVExtractor library is not available. Please install it with: pip install motion-vector-extractor")

        # 调用父类初始化方法
        super().__init__()

        # 初始化MVExtractor特有的VideoCap实例
        self.cap = VideoCap()

        # 初始化EMA状态
        self.motion_ema = None  # 运动指数移动平均值
        self.ema_alpha = 0.4    # EMA平滑因子

        # 初始化调试视频状态
        self.debug_video_writer = None
        self.debug_video_path = None

        # 初始化调试可视化
        self._init_debug_visualization()

    def _init_debug_visualization(self):
        """初始化调试可视化功能"""
        if Config.DEBUG_MVEXTRACTOR_VISUALIZATION:
            # 创建调试输出目录
            os.makedirs(Config.DEBUG_MVEXTRACTOR_OUTPUT_DIR, exist_ok=True)
            os.makedirs(Config.DEBUG_MVEXTRACTOR_VIDEO_OUTPUT_DIR, exist_ok=True)
            logger.info(f"MVExtractor调试可视化已启用，输出目录: {Config.DEBUG_MVEXTRACTOR_OUTPUT_DIR}")
            logger.info(f"MVExtractor调试视频输出目录: {Config.DEBUG_MVEXTRACTOR_VIDEO_OUTPUT_DIR}")

    def _init_debug_video(self, video_path, fps, width, height):
        """
        初始化调试视频写入器

        Args:
            video_path: 原始视频路径
            fps: 视频帧率
            width: 视频宽度
            height: 视频高度
        """
        if not Config.DEBUG_MVEXTRACTOR_VISUALIZATION:
            return

        try:
            # 生成调试视频文件名（保持原文件名，改变扩展名）
            video_basename = os.path.splitext(os.path.basename(video_path))[0]
            debug_video_filename = f"{video_basename}.mp4"
            self.debug_video_path = os.path.join(Config.DEBUG_MVEXTRACTOR_VIDEO_OUTPUT_DIR, debug_video_filename)

            # 初始化视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.debug_video_writer = cv2.VideoWriter(
                self.debug_video_path, fourcc, fps, (width, height)
            )

            if not self.debug_video_writer.isOpened():
                raise IOError(f"无法创建调试视频文件: {self.debug_video_path}")

            logger.info(f"MVExtractor调试视频初始化成功: {self.debug_video_path}")

        except Exception as e:
            logger.error(f"初始化MVExtractor调试视频失败: {e}")
            self.debug_video_writer = None
            self.debug_video_path = None

    def _close_debug_video(self):
        """关闭调试视频写入器"""
        if self.debug_video_writer is not None:
            try:
                self.debug_video_writer.release()
                logger.debug(f"MVExtractor调试视频已保存: {self.debug_video_path}")
            except Exception as e:
                logger.error(f"关闭MVExtractor调试视频失败: {e}")
            finally:
                self.debug_video_writer = None
                self.debug_video_path = None

    def _add_debug_frame(self, frame, motion_stats, motion_ema):
        """
        添加调试帧到视频

        Args:
            frame: 要添加的帧
            motion_stats: 运动统计信息
            motion_ema: 运动指数移动平均值
        """
        if not Config.DEBUG_MVEXTRACTOR_VISUALIZATION or self.debug_video_writer is None:
            return

        try:
            # 创建帧的副本用于调试可视化
            debug_frame = frame.copy()

            # 在图片左上角绘制运动统计信息（两行文本）
            # 第一行：当前帧运动幅值
            motion_text = f"Motion: {motion_stats['mean_magnitude']:.3f}"
            cv2.putText(debug_frame, motion_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                       (0, 255, 255), 2)  # 黄色文本，缩放0.7，线条粗细2

            # 第二行：指数移动平均值（垂直间距25像素）
            ema_text = f"EMA(3s): {motion_ema:.3f}"
            cv2.putText(debug_frame, ema_text, (10, 55),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                       (0, 255, 255), 2)  # 黄色文本，缩放0.7，线条粗细2

            # 写入调试视频
            self.debug_video_writer.write(debug_frame)

        except Exception as e:
            logger.error(f"添加MVExtractor调试帧失败: {e}")

    def _update_motion_ema(self, current_motion_magnitude):
        """
        更新运动指数移动平均值

        Args:
            current_motion_magnitude (float): 当前帧的运动幅值

        Returns:
            float: 更新后的EMA值
        """
        if self.motion_ema is None:
            # 第一帧，初始化EMA为当前值
            self.motion_ema = current_motion_magnitude
        else:
            # 使用EMA公式更新：EMA_new = α × current_value + (1-α) × EMA_old
            self.motion_ema = self.ema_alpha * current_motion_magnitude + (1 - self.ema_alpha) * self.motion_ema

        return self.motion_ema

    def release_resources(self):
        """
        释放所有资源

        这个方法是幂等的，可以安全地多次调用。
        """
        try:
            # 关闭调试视频写入器
            self._close_debug_video()
        except Exception as e:
            logger.error(f"释放调试视频资源失败: {e}")

        try:
            # 释放MVExtractor资源
            if hasattr(self, 'cap') and self.cap is not None:
                self.cap.release()
        except Exception as e:
            logger.error(f"释放MVExtractor资源失败: {e}")

    def calculate_motion_statistics(self, motion_vectors, width, height):
        """
        计算motion vector统计信息

        Args:
            motion_vectors (numpy.ndarray): motion vector数组

        Returns:
            dict: 包含运动统计信息
        """
        if len(motion_vectors) == 0:
            return {
                'mean_magnitude': 0.0,
            }

        # 计算每个motion vector的幅值
        mv = np.asarray(motion_vectors, dtype=float)
        w = mv[:, 1]
        h = mv[:, 2]
        mx = mv[:, 7]
        my = mv[:, 8]
        ms = mv[:, 9]
        dx = mx / ms
        dy = my / ms

        # 按块面积归一化： sqrt(w*h)
        block_diag = np.sqrt(w * h)
        block_diag[block_diag == 0] = 1  # 避免除零
        normalized_mags = np.sqrt(dx**2 + dy**2) / block_diag

        # 可选：按帧对角线进一步归一化
        #if width is not None and height is not None:
            #frame_diag = np.sqrt(width*width + height*height)
            #if frame_diag > 0:
                #normalized_mags = normalized_mags / frame_diag
        k = 1
        med = np.median(normalized_mags)
        std = np.std(normalized_mags)
        score = (med + k * std) / 2.0
        # 限制在 [0,1]
        score = max(0.0, min(1.0, score))

        return {
            'mean_magnitude': score
        }

    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧和运动向量

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
        """
        try:
            # 重置EMA状态（每个新视频开始时）
            self.motion_ema = None

            # 使用mvextractor打开视频
            ret = self.cap.open(video_path)
            if not ret:
                raise IOError(f"无法使用MVExtractor打开视频: {video_path}")

            # 获取视频信息
            video_info = self.get_video_info(video_path)
            total_frames = video_info['total_frames']
            fps = video_info['fps']
            width = video_info['width']
            height = video_info['height']

            # 初始化调试视频
            self._init_debug_video(video_path, fps, width, height)

            # 智能帧选择参数
            window_size = int(Config.SAMPLE_INTERVAL_SEC * fps)
            frame_idx = 0

            # 滑动窗口处理
            while True:
                window_frames = []  # 存储窗口内的帧数据

                # 收集一个窗口的帧
                for _ in range(window_size):
                    ret, frame, motion_vectors, _, _ = self.cap.read()

                    if not ret:
                        # 视频结束，处理剩余帧（如果有）
                        break

                    try:
                        motion_stats = self.calculate_motion_statistics(motion_vectors, width, height)

                        # 计算指数移动平均值
                        current_magnitude = motion_stats['mean_magnitude']
                        motion_ema = self._update_motion_ema(current_magnitude)

                        window_frames.append({
                            'frame': frame,
                            'global_idx': frame_idx,
                            'motion_stats': motion_stats,
                            'motion_ema': motion_ema,
                        })

                        # 添加每一帧到调试视频
                        self._add_debug_frame(frame, motion_stats, motion_ema)

                    except Exception as e:
                        logger.error(f"视频编解码错误: 处理第 {frame_idx} 帧失败: {e}")

                    frame_idx += 1

                # 智能帧选择：选择运动幅值最小的帧
                if window_frames:
                    # 直接比较找到最小运动幅值的帧
                    selected_frame_data = window_frames[0]
                    min_magnitude = selected_frame_data['motion_ema']

                    for frame_data in window_frames[1:]:
                        mean_magnitude = frame_data['motion_ema']
                        if mean_magnitude < min_magnitude:
                            min_magnitude = mean_magnitude
                            selected_frame_data = frame_data

                    if selected_frame_data['motion_ema'] < Config.MOTION_THRESHOLD:

                        frame_info = {
                            'global_idx': selected_frame_data['global_idx'],
                            'total_frames': total_frames,
                            'fps': fps,
                            'motion_statistics': selected_frame_data['motion_stats'],
                            'motion_ema': selected_frame_data['motion_ema'],
                        }
                        yield selected_frame_data['frame'], frame_info
                else:
                    break

                # 如果读取的帧数少于窗口大小，说明视频已结束
                if len(window_frames) < window_size:
                    break

        except Exception as e:
            raise IOError(f"无法使用MVExtractor处理视频: {video_path}, 错误: {e}")
        finally:
            try:
                self.cap.release()
            except Exception as e:
                logger.error(f"视频编解码错误: MVExtractor 资源释放失败: {e}")

            # 关闭调试视频
            self._close_debug_video()
