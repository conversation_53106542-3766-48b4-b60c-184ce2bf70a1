#!/usr/bin/env python3
"""
视频处理器工厂模块
"""

from .base import BaseVideoProcessor
from .cv2_processor import CV2VideoProcessor
from .decord_processor import DecordVideoProcessor
from .mvextractor_processor import MVExtractorVideoProcessor


def create_video_processor(processor_type: str = "decord") -> BaseVideoProcessor:
    """
    创建视频处理器的工厂函数

    Args:
        processor_type (str): 处理器类型，可选 "cv2"、"decord" 或 "mvextractor"，默认为 "decord"

    Returns:
        BaseVideoProcessor: 视频处理器实例

    Raises:
        ValueError: 不支持的处理器类型
        ImportError: 相关库不可用
    """
    if processor_type.lower() == "cv2":
        return CV2VideoProcessor()
    elif processor_type.lower() == "decord":
        return DecordVideoProcessor()
    elif processor_type.lower() == "mvextractor":
        return MVExtractorVideoProcessor()
    else:
        raise ValueError(f"不支持的处理器类型: {processor_type}. 支持的类型: 'cv2', 'decord', 'mvextractor'")
