#!/usr/bin/env python3
"""
基于OpenCV的视频处理器模块
"""

import cv2
import numpy as np
from typing import Generator, Tuple, Dict, Any
from config.settings import Config
from loguru import logger

from .base import BaseVideoProcessor


class CV2VideoProcessor(BaseVideoProcessor):
    """基于OpenCV的视频处理器类"""

    def __init__(self):
        pass

    def release_resources(self):
        """
        释放所有资源

        CV2VideoProcessor在__call__方法中使用局部VideoCapture对象，
        不需要释放全局资源，但保持接口一致性。
        """
        pass

    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 获取视频基本参数
        video_info = self.get_video_info(video_path)
        total_frames = video_info['total_frames']
        fps = video_info['fps']

        sample_step = max(1, int(fps * Config.SAMPLE_INTERVAL_SEC))

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        try:
            global_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if global_idx % sample_step == 0:
                    frame_info = {
                        'global_idx': global_idx,
                        'total_frames': total_frames,
                        'fps': fps
                    }
                    yield frame, frame_info

                global_idx += 1

                if global_idx >= total_frames:
                    break

        except Exception as e:
            logger.error(f"视频编解码错误: CV2视频处理失败: {e}")
            raise IOError(f"无法使用CV2处理视频: {video_path}, 错误: {e}")
        finally:
            cap.release()

    def get_frame_by_index(self, video_path: str, frame_index: int) -> np.ndarray:
        """
        根据给定的帧号直接返回对应的视频帧

        Args:
            video_path (str): 视频文件路径
            frame_index (int): 帧号（从0开始的索引）

        Returns:
            np.ndarray: numpy数组格式的图像帧，形状为(H, W, C)，数据类型为uint8

        Raises:
            FileNotFoundError: 视频文件不存在
            IOError: 无法打开视频文件
            IndexError: 帧号超出视频总帧数范围
            ValueError: 帧号为负数
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        if frame_index < 0:
            raise ValueError(f"帧号不能为负数: {frame_index}")

        # 获取视频信息
        video_info = self.get_video_info(video_path)
        total_frames = video_info['total_frames']

        if frame_index >= total_frames:
            raise IndexError(f"帧号 {frame_index} 超出视频总帧数 {total_frames}")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        try:
            # 直接跳转到指定帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)

            ret, frame = cap.read()
            if not ret:
                raise IOError(f"无法读取第 {frame_index} 帧")

            return frame

        except Exception as e:
            # 重新抛出已知异常类型，包装未知异常
            if isinstance(e, (IndexError, IOError)):
                raise
            else:
                raise IOError(f"读取帧时发生错误: {e}")
        finally:
            cap.release()

    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频的基本信息

        Args:
            video_path (str): 视频文件路径

        Returns:
            Dict[str, Any]: 包含视频信息的字典，包含以下键值：
                - total_frames (int): 视频总帧数
                - fps (float): 视频帧率
                - width (int): 视频宽度
                - height (int): 视频高度
                - duration (float): 视频时长（秒）

        Raises:
            FileNotFoundError: 视频文件不存在
            IOError: 无法打开视频文件

        Example:
            >>> processor = CV2VideoProcessor()
            >>> info = processor.get_video_info("video.mp4")
            >>> print(f"总帧数: {info['total_frames']}, 帧率: {info['fps']}")
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        try:
            # 获取视频基本信息
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = float(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 计算视频时长
            duration = total_frames / fps if fps > 0 else 0.0

            return {
                'total_frames': total_frames,
                'fps': fps,
                'width': width,
                'height': height,
                'duration': duration
            }

        except Exception as e:
            # 包装未知异常为IOError
            if isinstance(e, (FileNotFoundError, IOError)):
                raise
            else:
                raise IOError(f"获取视频信息时发生错误: {e}")
        finally:
            cap.release()
