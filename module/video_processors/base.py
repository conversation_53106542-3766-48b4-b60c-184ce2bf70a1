#!/usr/bin/env python3
"""
视频处理器抽象基类模块
"""

import os
from abc import ABC, abstractmethod
from typing import Generator, Tuple, Dict, Any


class BaseVideoProcessor(ABC):
    """视频处理器抽象基类"""

    @abstractmethod
    def __init__(self):
        """初始化视频处理器"""
        pass

    @abstractmethod
    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧的抽象方法

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
                frame: 视频帧数据
                video_info_dict: 包含视频信息的字典，至少包含：
                    - global_idx: 全局帧索引
                    - fps: 帧率
                    - total_frames: 总帧数
        """
        pass

    @abstractmethod
    def get_frame_by_index(self, video_path: str, frame_index: int):
        """
        根据给定的帧号直接返回对应的视频帧

        Args:
            video_path (str): 视频文件路径
            frame_index (int): 帧号（从0开始的索引）

        Returns:
            numpy.ndarray: 图像帧数据

        Raises:
            FileNotFoundError: 视频文件不存在
            IOError: 无法打开视频文件
            IndexError: 帧号超出视频总帧数范围
            ValueError: 帧号为负数
        """
        pass

    @abstractmethod
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频的基本信息

        Args:
            video_path (str): 视频文件路径

        Returns:
            Dict[str, Any]: 包含视频信息的字典，包含以下键值：
                - total_frames (int): 视频总帧数
                - fps (float): 视频帧率
                - width (int): 视频宽度
                - height (int): 视频高度
                - duration (float): 视频时长（秒）

        Raises:
            FileNotFoundError: 视频文件不存在
            IOError: 无法打开视频文件
        """
        pass

    def validate_video_path(self, video_path: str) -> bool:
        """
        验证视频文件路径是否有效

        Args:
            video_path (str): 视频文件路径

        Returns:
            bool: 路径是否有效
        """
        return os.path.exists(video_path) and os.path.isfile(video_path)

    def release_resources(self):
        """
        释放所有资源的方法

        这个方法应该被所有子类重写以释放特定的资源。
        该方法应该是幂等的（可以安全地多次调用）。
        """
        pass

    def __del__(self):
        """
        析构函数，确保资源被释放
        """
        try:
            self.release_resources()
        except Exception:
            # 在析构函数中忽略异常，避免程序崩溃
            pass
