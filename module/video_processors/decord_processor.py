#!/usr/bin/env python3
"""
基于Decord的视频处理器模块
"""

import cv2
import numpy as np
from typing import Generator, Tuple, Dict, Any
from config.settings import Config
from loguru import logger

from decord import VideoReader, cpu


from .base import BaseVideoProcessor


class DecordVideoProcessor(BaseVideoProcessor):
    """基于Decord的视频处理器类"""

    def __init__(self):
        pass

    def release_resources(self):
        """
        释放所有资源

        DecordVideoProcessor在__call__方法中使用局部VideoReader对象，
        不需要释放全局资源，但保持接口一致性。
        """
        pass

    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 获取视频基本参数
        video_info = self.get_video_info(video_path)
        total_frames = video_info['total_frames']
        fps = video_info['fps']

        sample_step = max(1, int(fps * Config.SAMPLE_INTERVAL_SEC))

        try:
            # 使用decord打开视频
            vr = VideoReader(video_path, ctx=cpu(0), num_threads=16)

            for global_idx, frame in enumerate(vr):

                if global_idx % sample_step == 0:
                    # 使用decord读取指定帧
                    frame = frame.asnumpy()

                    # 将RGB转换为BGR (decord默认返回RGB，而OpenCV使用BGR)
                    frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                    frame_info = {
                        'global_idx': global_idx,
                        'total_frames': total_frames,
                        'fps': fps
                    }

                    yield frame, frame_info

        except Exception as e:
            logger.error(f"视频编解码错误: Decord视频处理失败: {e}")
            raise IOError(f"无法使用Decord打开视频: {video_path}, 错误: {e}")

    def get_frame_by_index(self, video_path: str, frame_index: int) -> np.ndarray:
        """
        根据给定的帧号直接返回对应的视频帧

        Args:
            video_path (str): 视频文件路径
            frame_index (int): 帧号（从0开始的索引）

        Returns:
            np.ndarray: numpy数组格式的图像帧，形状为(H, W, C)，数据类型为uint8

        Raises:
            FileNotFoundError: 视频文件不存在
            IOError: 无法打开视频文件
            IndexError: 帧号超出视频总帧数范围
            ValueError: 帧号为负数
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        if frame_index < 0:
            raise ValueError(f"帧号不能为负数: {frame_index}")

        # 获取视频信息
        video_info = self.get_video_info(video_path)
        total_frames = video_info['total_frames']

        if frame_index >= total_frames:
            raise IndexError(f"帧号 {frame_index} 超出视频总帧数 {total_frames}")

        try:
            # 使用decord打开视频
            vr = VideoReader(video_path, ctx=cpu(0), num_threads=16)

            # 直接读取指定帧（decord支持随机访问）
            frame = vr[frame_index].asnumpy()

            # 将RGB转换为BGR (decord默认返回RGB，而OpenCV使用BGR)
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

            return frame

        except IndexError:
            # 重新抛出IndexError，保持原始错误信息
            raise
        except ValueError:
            # 重新抛出ValueError，保持原始错误信息
            raise
        except Exception as e:
            raise IOError(f"无法使用Decord读取第 {frame_index} 帧: {video_path}, 错误: {e}")

    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频的基本信息

        Args:
            video_path (str): 视频文件路径

        Returns:
            Dict[str, Any]: 包含视频信息的字典，包含以下键值：
                - total_frames (int): 视频总帧数
                - fps (float): 视频帧率
                - width (int): 视频宽度
                - height (int): 视频高度
                - duration (float): 视频时长（秒）

        Raises:
            FileNotFoundError: 视频文件不存在
            IOError: 无法打开视频文件

        Example:
            >>> processor = DecordVideoProcessor()
            >>> info = processor.get_video_info("video.mp4")
            >>> print(f"总帧数: {info['total_frames']}, 帧率: {info['fps']}")
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        try:
            # 使用decord打开视频
            vr = VideoReader(video_path, ctx=cpu(0), num_threads=16)

            # 获取视频基本信息
            total_frames = len(vr)
            fps = float(vr.get_avg_fps())
            width = int(vr[0].shape[1])  # 获取第一帧的宽度
            height = int(vr[0].shape[0])  # 获取第一帧的高度

            # 计算视频时长
            duration = total_frames / fps if fps > 0 else 0.0

            return {
                'total_frames': total_frames,
                'fps': fps,
                'width': width,
                'height': height,
                'duration': duration
            }

        except Exception as e:
            raise IOError(f"无法使用Decord获取视频信息: {video_path}, 错误: {e}")
