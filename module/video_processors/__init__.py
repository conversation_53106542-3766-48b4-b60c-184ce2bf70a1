#!/usr/bin/env python3
"""
视频处理器模块包

提供多种视频处理器实现：
- BaseVideoProcessor: 抽象基类
- CV2VideoProcessor: 基于OpenCV的实现
- DecordVideoProcessor: 基于Decord的实现
- MVExtractorVideoProcessor: 基于MVExtractor的实现，支持运动向量提取

工厂函数：
- create_video_processor: 创建指定类型的视频处理器
"""

from .base import BaseVideoProcessor
from .cv2_processor import CV2VideoProcessor
from .decord_processor import DecordVideoProcessor
from .mvextractor_processor import MVExtractorVideoProcessor, MVEXTRACTOR_AVAILABLE
from .factory import create_video_processor

# 导出所有公共接口
__all__ = [
    'BaseVideoProcessor',
    'CV2VideoProcessor',
    'DecordVideoProcessor',
    'MVExtractorVideoProcessor',
    'create_video_processor',
    'MVEXTRACTOR_AVAILABLE'
]

# 版本信息
__version__ = '1.0.0'
