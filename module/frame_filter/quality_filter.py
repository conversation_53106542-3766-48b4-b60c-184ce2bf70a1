#!/usr/bin/env python3
"""
质量帧过滤器模块
"""

import torch
import cvcuda
from typing import Dict, Any, <PERSON><PERSON>
import numpy as np

from .base import BaseFrameFilter
from config.settings import Config


class QualityFrameFilter(BaseFrameFilter):
    """质量帧过滤器 - 基于亮度和对比度过滤低质量帧"""

    def __init__(self, **kwargs):
        """
        初始化质量帧过滤器

        Args:
            min_brightness (float): 最小亮度阈值，默认使用配置文件中的值
            min_contrast (float): 最小对比度阈值，默认使用配置文件中的值
            **kwargs: 预留的扩展参数
        """
        self.filter_name = "QualityFrameFilter"

    def __call__(self, frame: np.ndarray, frame_info: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        应用质量过滤

        Args:
            frame (np.ndarray): 输入帧
            frame_info (Dict[str, Any]): 帧信息字典

        Returns:
            Tu<PERSON>[bool, Dict[str, Any]]:
                - is_valid: 当前帧是否通过过滤器
                - filter_info: 过滤器调试信息字典
        """
        try:
            # 检查帧质量
            brightness, contrast, sharpness = self.check_brightness_and_contrast(frame)

            # 转换为浮点数进行比较
            brightness_val = float(brightness)
            contrast_val = float(contrast)
            sharpness_val = float(sharpness)
            
            # 判断是否通过质量检查
            brightness_pass = brightness_val > Config.MIN_BRIGHTNESS
            contrast_pass = contrast_val > Config.MIN_CONTRAST
            overall_pass = brightness_pass and contrast_pass
            
            # 构建调试信息
            framefilter_info = {
                'brightness': {
                    'value': brightness_val
                },
                'contrast': {
                    'value': contrast_val
                },
                'sharpness': {
                    'value': sharpness_val
                },
            }
            
            # 返回结果
            return overall_pass, framefilter_info

        except Exception as e:
            # 异常情况下的调试信息
            framefilter_info = {
                'global_idx': frame_info.get('global_idx', -1),
                'error': str(e)
            }
            return False, framefilter_info


    def check_brightness_and_contrast(self, frame):
        """
        检查图像亮度和对比度

        Args:
            frame (numpy.ndarray): 输入图像

        Returns:
            tuple: (亮度, 对比度标准差, 清晰度)
        """
        # 转为 HWC 布局，确保内存连续
        frame_hwc: torch.Tensor = torch.from_numpy(frame).contiguous().to("cuda")

        # 包装为 CVCUDA Tensor，布局声明为 "HWC"
        frame_tensor = cvcuda.as_tensor(frame_hwc, layout="HWC")

        gray = cvcuda.cvtcolor(frame_tensor, cvcuda.ColorConversion.BGR2GRAY)

        # 转 PyTorch GPU Tensor 并计算平均亮度
        gray_t = torch.as_tensor(gray.cuda()).squeeze(-1).float() * 100.0 / 255.0
        brightness = torch.mean(gray_t)  # 全局平均亮度

        # 对比度图：|pixel - brightness|
        contrast_map = torch.abs(gray_t - brightness)
        contrast_std = torch.std(contrast_map)

        # 清晰度评估：Laplacian + 方差
        lap = cvcuda.laplacian(gray, ksize=3, scale=1.0)
        lap_t = torch.as_tensor(lap.cuda()).float()
        sharpness = torch.var(lap_t)  # 方差代表高频成分强度

        return brightness, contrast_std, sharpness
