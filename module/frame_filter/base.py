#!/usr/bin/env python3
"""
帧过滤器基类模块
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple
import numpy as np

class BaseFrameFilter(ABC):
    """帧过滤器抽象基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """
        初始化帧过滤器

        Args:
            **kwargs: 过滤器特定的初始化参数
        """
        pass

    @abstractmethod
    def __call__(self, frame: np.ndarray, frame_info: Dict[str, Any]) -> <PERSON>ple[bool, Dict[str, Any]]:
        """
        帧过滤器主要处理方法

        Args:
            frame (np.ndarray): 输入帧
            frame_info (Dict[str, Any]): 帧信息字典（来自video_processor）

        Returns:
            Tuple[bool, Dict[str, Any]]:
                - is_valid: 当前帧是否通过过滤器
                - filter_info: 过滤器调试信息字典
        """
        pass



