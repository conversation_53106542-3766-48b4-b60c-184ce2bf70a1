#!/usr/bin/env python3
"""
人脸过滤器链模块
"""

from typing import List, Dict, Any, Tuple
import numpy as np

# 创建日志记录器
from loguru import logger

from .face_filter.base import BaseFaceFilter
from .face_filter.score_filter import ScoreFaceFilter
from .face_filter.geometry_validator import GeometryFaceFilter
from classes.retinaface_result import RetinaFaceResult
from config.settings import Config


class FaceFilterChain:
    """人脸过滤器链，用于组合多个过滤器"""
    
    def __init__(self, filters: List[BaseFaceFilter]):
        """
        初始化过滤器链

        Args:
            filters (List[BaseFaceFilter]): 过滤器列表
        """
        self.filters = filters
        self.max_score = len(filters)  # 过滤器链的满分分值（等于过滤器数量）
    
    def __call__(self, frame: np.ndarray, face_results: List[RetinaFaceResult]) -> Tu<PERSON>[float, Dict[str, Any]]:
        """
        依次应用所有人脸过滤器，计算最高得分

        Args:
            frame (np.ndarray): 输入帧
            face_results (List[RetinaFaceResult]): RetinaFace检测结果列表

        Returns:
            Tuple[float, Dict[str, Any]]:
                - highest_score: 所有人脸的最高得分
                - all_filter_info: 所有过滤器的详细信息
        """
        # 如果没有人脸结果，直接返回0.0
        if not face_results:
            return 0.0, {}

        # 如果没有过滤器，返回0.0（没有评分标准）
        if not self.filters:
            return 0.0, {}

        # 为每个人脸结果维护一个累计得分列表
        face_scores = [0.0] * len(face_results)
        all_filter_info = {}

        # 依次应用所有过滤器
        for filter_instance in self.filters:
            scores, filter_info = filter_instance(frame, face_results)

            # 记录每个过滤器的信息
            filter_key = filter_instance.filter_name
            all_filter_info[filter_key] = filter_info

            # 将每个过滤器返回的分数累加到对应人脸的总分中
            for i, score in enumerate(scores):
                face_scores[i] += score

        # 计算所有人脸的最高得分
        highest_score = max(face_scores) if face_scores else 0.0

        return highest_score, all_filter_info


def create_face_filter_chain() -> FaceFilterChain:
    """
    根据配置初始化人脸过滤器链

    Returns:
        FaceFilterChain: 初始化的过滤器链
    """

    # 创建过滤器实例映射
    filter_map = {
        'BasicFilter': ScoreFaceFilter(),
        'GeometryFilter': GeometryFaceFilter()
    }

    # 根据配置创建过滤器列表
    filters = []
    for filter_name in Config.FILTER_CHAIN_COMPOSITION:
        if filter_name in filter_map:
            filters.append(filter_map[filter_name])
        else:
            logger.warning(f"未知的过滤器类型 '{filter_name}'，已跳过")

    # 总是创建过滤链，即使为空
    return FaceFilterChain(filters)
