#!/bin/bash

# 实际 ffmpeg 可执行文件路径
REAL_FFMPEG="/usr/local/bin/ffmpeg"

# 收集原始参数
ORIG_ARGS=("$@")

# 日志文件
LOGFILE="/home/<USER>/video_summerization/test_script/face_analyzer/log-20250418.txt"

# 记录输入命令
echo "[INPUT ] ffmpeg ${ORIG_ARGS[*]}" >> "$LOGFILE"

# 标志位
USE_LIBX264=0
USE_LIBWEBP=0
HAS_T_OPTION=0
RESOLUTION_PARAM=""
INPUT_FILE=""
OUTPUT_FILE=""

# 视频分析服务器配置
VIDEO_ANALYSIS_HOST="localhost"
VIDEO_ANALYSIS_PORT="5000"
VIDEO_ANALYSIS_TIMEOUT="3600"

# 分辨率解析函数
parse_vf_scale() {
    local vf_param="$1"
    # 从 -vf 参数中提取 scale=WIDTHxHEIGHT 或 scale=WIDTH:HEIGHT
    local scale_pattern="scale=([0-9]+)[x:]([0-9]+)"
    if [[ "$vf_param" =~ $scale_pattern ]]; then
        local width="${BASH_REMATCH[1]}"
        local height="${BASH_REMATCH[2]}"
        echo "${width}x${height}"
        return 0
    fi
    return 1
}

parse_s_param() {
    local s_param="$1"
    # 从 -s 参数中提取 WIDTHxHEIGHT
    local size_pattern="^([0-9]+)x([0-9]+)$"
    if [[ "$s_param" =~ $size_pattern ]]; then
        echo "$s_param"
        return 0
    fi
    return 1
}

# 重试配置（硬编码）
MAX_RETRIES=3
RETRY_INTERVAL=10

# 检测是否使用 libx264 或 libwebp，并提取输入和输出文件
for ((i=0; i<${#ORIG_ARGS[@]}; i++)); do
    case "${ORIG_ARGS[$i]}" in
        -i)
            INPUT_FILE="${ORIG_ARGS[$((i+1))]}"  # 第一个输入文件
            ;;
        -vcodec|-c:v)
            if [[ "${ORIG_ARGS[$((i+1))]}" == "libx264" ]]; then
                USE_LIBX264=1
            elif [[ "${ORIG_ARGS[$((i+1))]}" == "libwebp" ]]; then
                USE_LIBWEBP=1
            fi
            ;;
        -t)
            HAS_T_OPTION=1
            ;;
        -vf)
            # 解析 -vf 参数中的 scale 信息
            vf_value="${ORIG_ARGS[$((i+1))]}"
            if [[ -n "$vf_value" ]]; then
                resolution=$(parse_vf_scale "$vf_value")
                if [[ $? -eq 0 && -n "$resolution" ]]; then
                    RESOLUTION_PARAM="$resolution"
                    echo "[RESOLUTION] 从 -vf 参数捕捉到分辨率: $RESOLUTION_PARAM" >> "$LOGFILE"
                fi
            fi
            ;;
        -s)
            # 解析 -s 参数
            s_value="${ORIG_ARGS[$((i+1))]}"
            if [[ -n "$s_value" ]]; then
                resolution=$(parse_s_param "$s_value")
                if [[ $? -eq 0 && -n "$resolution" ]]; then
                    RESOLUTION_PARAM="$resolution"
                    echo "[RESOLUTION] 从 -s 参数捕捉到分辨率: $RESOLUTION_PARAM" >> "$LOGFILE"
                fi
            fi
            ;;
    esac
done

# 提取输出文件（通常是最后一个参数）
OUTPUT_FILE="${ORIG_ARGS[-1]}"

# 重试执行函数（简化版，无彩色输出）
retry_operation() {
    local max_retries="$1"
    local retry_interval="$2"
    local operation_name="$3"
    shift 3
    local command=("$@")

    local attempt=1

    while [ $attempt -le $max_retries ]; do
        if [ $attempt -gt 1 ]; then
            echo "[RETRY] 第 $attempt 次尝试 $operation_name" >> "$LOGFILE"
        else
            echo "[RETRY] 开始 $operation_name" >> "$LOGFILE"
        fi

        # 执行命令
        "${command[@]}"
        local exit_code=$?

        if [ $exit_code -eq 0 ]; then
            if [ $attempt -gt 1 ]; then
                echo "[RETRY] $operation_name 在第 $attempt 次尝试后成功" >> "$LOGFILE"
            fi
            return 0
        fi

        if [ $attempt -lt $max_retries ]; then
            echo "[RETRY] $operation_name 失败 (尝试 $attempt/$max_retries)，${retry_interval}秒后重试" >> "$LOGFILE"
            sleep $retry_interval
        else
            echo "[RETRY] $operation_name 在 $max_retries 次尝试后仍然失败" >> "$LOGFILE"
        fi

        ((attempt++))
    done

    return 1
}

# 视频分析功能函数
# 发送视频分析请求到服务器（单次尝试）
send_video_analysis_request_single() {
    local input_path="$1"
    local output_path="$2"
    local resolution="$3"
    local url="http://${VIDEO_ANALYSIS_HOST}:${VIDEO_ANALYSIS_PORT}/analyze"

    # 获取绝对路径
    input_path=$(realpath "$input_path" 2>/dev/null || echo "$input_path")
    output_path=$(realpath "$output_path" 2>/dev/null || echo "$output_path")

    # 构建 JSON 请求体
    if [[ -n "$resolution" ]]; then
        json_data=$(cat <<EOF
{
    "input_path": "$input_path",
    "output_path": "$output_path",
    "resolution": "$resolution"
}
EOF
)
    else
        json_data=$(cat <<EOF
{
    "input_path": "$input_path",
    "output_path": "$output_path"
}
EOF
)
    fi

    # 发送 HTTP POST 请求
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        --connect-timeout 30 \
        --max-time "$VIDEO_ANALYSIS_TIMEOUT" \
        "$url" 2>/dev/null)

    # 检查 curl 执行结果
    if [ $? -ne 0 ]; then
        echo "[VIDEO_ANALYSIS] 请求失败：$input_path" >> "$LOGFILE"
        return 1
    fi

    # 解析响应
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ]; then
        echo "[VIDEO_ANALYSIS] 视频分析完成" >> "$LOGFILE"

        # 检查输出文件是否生成
        if [ -f "$output_path" ]; then
            file_size=$(du -h "$output_path" 2>/dev/null | cut -f1 || echo "未知")
            echo "[VIDEO_ANALYSIS] 输出文件已生成: $output_path (大小: $file_size)" >> "$LOGFILE"
            return 0
        else
            echo "[VIDEO_ANALYSIS] 输出文件未找到: $output_path" >> "$LOGFILE"
            return 1
        fi
    else
        echo "[VIDEO_ANALYSIS] 服务器返回错误状态码: $http_code" >> "$LOGFILE"
        echo "[VIDEO_ANALYSIS] 错误详情: $body" >> "$LOGFILE"
        return 1
    fi
}

# 发送单帧分析请求到服务器（单次尝试）
send_frame_analysis_request_single() {
    local input_path="$1"
    local output_path="$2"
    local resolution="$3"
    local url="http://${VIDEO_ANALYSIS_HOST}:${VIDEO_ANALYSIS_PORT}/analyze_frame"

    # 获取绝对路径
    input_path=$(realpath "$input_path" 2>/dev/null || echo "$input_path")
    output_path=$(realpath "$output_path" 2>/dev/null || echo "$output_path")

    # 构建 JSON 请求体
    if [[ -n "$resolution" ]]; then
        json_data=$(cat <<EOF
{
    "input_path": "$input_path",
    "output_path": "$output_path",
    "resolution": "$resolution"
}
EOF
)
    else
        json_data=$(cat <<EOF
{
    "input_path": "$input_path",
    "output_path": "$output_path"
}
EOF
)
    fi

    # 发送 HTTP POST 请求
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        --connect-timeout 30 \
        --max-time "$VIDEO_ANALYSIS_TIMEOUT" \
        "$url" 2>/dev/null)

    # 检查 curl 执行结果
    if [ $? -ne 0 ]; then
        echo "[FRAME_ANALYSIS] 请求失败：$input_path" >> "$LOGFILE"
        return 1
    fi

    # 解析响应
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ]; then
        echo "[FRAME_ANALYSIS] 单帧分析完成" >> "$LOGFILE"

        # 检查输出文件是否生成
        if [ -f "$output_path" ]; then
            file_size=$(du -h "$output_path" 2>/dev/null | cut -f1 || echo "未知")
            echo "[FRAME_ANALYSIS] 输出文件已生成: $output_path (大小: $file_size)" >> "$LOGFILE"
            return 0
        else
            echo "[FRAME_ANALYSIS] 输出文件未找到: $output_path" >> "$LOGFILE"
            return 1
        fi
    else
        echo "[FRAME_ANALYSIS] 服务器返回错误状态码: $http_code" >> "$LOGFILE"
        echo "[FRAME_ANALYSIS] 错误详情: $body" >> "$LOGFILE"
        return 1
    fi
}

# 发送视频分析请求到服务器（带重试）
send_video_analysis_request() {
    local input_path="$1"
    local output_path="$2"
    local resolution="$3"

    # 验证输入文件
    if [ ! -f "$input_path" ]; then
        echo "[VIDEO_ANALYSIS] 错误: 输入文件不存在: $input_path" >> "$LOGFILE"
        return 1
    fi

    if [[ -n "$resolution" ]]; then
        echo "[VIDEO_ANALYSIS] 开始视频分析: $input_path -> $output_path (分辨率: $resolution)" >> "$LOGFILE"
    else
        echo "[VIDEO_ANALYSIS] 开始视频分析: $input_path -> $output_path" >> "$LOGFILE"
    fi
    echo "[VIDEO_ANALYSIS] 发送请求到: http://${VIDEO_ANALYSIS_HOST}:${VIDEO_ANALYSIS_PORT}/analyze" >> "$LOGFILE"

    # 使用重试机制
    retry_operation "$MAX_RETRIES" "$RETRY_INTERVAL" "视频分析" send_video_analysis_request_single "$input_path" "$output_path" "$resolution"
    return $?
}

# 发送单帧分析请求到服务器（带重试）
send_frame_analysis_request() {
    local input_path="$1"
    local output_path="$2"
    local resolution="$3"

    # 验证输入文件
    if [ ! -f "$input_path" ]; then
        echo "[FRAME_ANALYSIS] 错误: 输入文件不存在: $input_path" >> "$LOGFILE"
        return 1
    fi

    if [[ -n "$resolution" ]]; then
        echo "[FRAME_ANALYSIS] 开始单帧分析: $input_path -> $output_path (分辨率: $resolution)" >> "$LOGFILE"
    else
        echo "[FRAME_ANALYSIS] 开始单帧分析: $input_path -> $output_path" >> "$LOGFILE"
    fi
    echo "[FRAME_ANALYSIS] 发送请求到: http://${VIDEO_ANALYSIS_HOST}:${VIDEO_ANALYSIS_PORT}/analyze_frame" >> "$LOGFILE"

    # 使用重试机制
    retry_operation "$MAX_RETRIES" "$RETRY_INTERVAL" "单帧分析" send_frame_analysis_request_single "$input_path" "$output_path" "$resolution"
    return $?
}

# 将参数分为输入部分和输出部分
INPUT_PART=()
OUTPUT_PART=()
last_i=-1
for i in "${!ORIG_ARGS[@]}"; do
    if [[ "${ORIG_ARGS[$i]}" == "-i" ]]; then
        last_i=$i
    fi
done
# 包含 "-i" 及其文件名
end_index=$((last_i + 1))
for ((i=0; i<=end_index; i++)); do
    INPUT_PART+=("${ORIG_ARGS[$i]}")
done
# 其余为输出参数及输出文件
for ((i=end_index+1; i<${#ORIG_ARGS[@]}; i++)); do
    OUTPUT_PART+=("${ORIG_ARGS[$i]}")
done

# 重新组装参数：先输入部分
NEW_ARGS=("${INPUT_PART[@]}")

# 在输入之后插入 libx264 的 GOP 控制参数
if [[ $USE_LIBX264 -eq 1 ]]; then
    NEW_ARGS+=("-g" "30" "-keyint_min" "30" "-sc_threshold" "0")
fi

# 假设在脚本中，OUTPUT_PART 数组已经被填充，最后一个元素是输出文件名
OUT_FILE="${OUTPUT_PART[-1]}"

# 如果输出文件后缀是 .webp（忽略大小写），也认为要用 libwebp
if [[ "${OUT_FILE,,}" == *.webp ]]; then
    USE_LIBWEBP=1
fi

# 新的 USE_LIBWEBP 处理逻辑：集成视频分析功能
if [[ $USE_LIBWEBP -eq 1 && -n "$INPUT_FILE" && -n "$OUTPUT_FILE" ]]; then
    # 根据是否有 -t 选项决定使用哪种模式
    if [[ $HAS_T_OPTION -eq 1 ]]; then
        echo "[WEBP_PROCESSING] 检测到 libwebp 使用和 -t 选项，尝试视频分析服务器（动画模式）" >> "$LOGFILE"
        analysis_mode="动画"
        analysis_function="send_video_analysis_request"
    else
        echo "[WEBP_PROCESSING] 检测到 libwebp 使用，无 -t 选项，尝试单帧分析服务器（单帧模式）" >> "$LOGFILE"
        analysis_mode="单帧"
        analysis_function="send_frame_analysis_request"
    fi

    # 确保输出文件以 .webp 结尾
    if [[ ! "${OUTPUT_FILE,,}" == *.webp ]]; then
        OUTPUT_FILE="${OUTPUT_FILE}.webp"
        echo "[WEBP_PROCESSING] 自动添加 .webp 扩展名: $OUTPUT_FILE" >> "$LOGFILE"
    fi

    # 创建输出目录
    output_dir=$(dirname "$OUTPUT_FILE")
    if [ ! -d "$output_dir" ]; then
        mkdir -p "$output_dir" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "[WEBP_PROCESSING] 已创建输出目录: $output_dir" >> "$LOGFILE"
        else
            echo "[WEBP_PROCESSING] 警告: 无法创建输出目录: $output_dir" >> "$LOGFILE"
        fi
    fi

    # 尝试使用相应的分析服务器
    if $analysis_function "$INPUT_FILE" "$OUTPUT_FILE" "$RESOLUTION_PARAM"; then
        echo "[WEBP_PROCESSING] ${analysis_mode}分析服务器处理成功，跳过 FFmpeg 处理" >> "$LOGFILE"
        exit 0
    else
        echo "[WEBP_PROCESSING] ${analysis_mode}分析服务器处理失败，回退到 FFmpeg 处理" >> "$LOGFILE"
        # 继续执行原有的 FFmpeg 逻辑
        DURATION=$(ffprobe -v error -show_entries format=duration -of csv=p=0 "$INPUT_FILE" 2>/dev/null)
        if [[ -n "$DURATION" ]]; then
            echo "[DURATION] $INPUT_FILE: ${DURATION}s" >> "$LOGFILE"
            D_INT=${DURATION%%.*}
            if (( D_INT > 30 )); then
                NEW_ARGS+=("-ss" "30")
            fi
        fi
    fi
fi

# 追加输出参数和输出文件
NEW_ARGS+=("${OUTPUT_PART[@]}")

# 最终命令
FINAL_CMD=("$REAL_FFMPEG" "${NEW_ARGS[@]}")
# 记录输出命令
echo "[OUTPUT] ${FINAL_CMD[*]}" >> "$LOGFILE"

# 执行真实 ffmpeg
exec "${FINAL_CMD[@]}"